namespace HOTELSV_BE.Models
{
    public class Role
    {
        public int RoleId { get; set; }
        public string RoleName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    public class AddRoleRequest
    {
        public string RoleName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    public class AddRoleResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int RoleId { get; set; }
    }

    public class UpdateRoleRequest
    {
        public int RoleId { get; set; }
        public string? RoleName { get; set; }
        public string? Description { get; set; }
    }

    public class UpdateRoleResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public Role? Role { get; set; }
    }

    public class DeleteRoleResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class GetRoleResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public Role? Role { get; set; }
    }

    public class GetAllRolesResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<Role> Roles { get; set; } = new();
    }
}
