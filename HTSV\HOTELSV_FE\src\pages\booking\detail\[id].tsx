import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useAuth } from '../../../contexts/AuthContext';
import Navbar from '../../../components/Navbar';
import styles from '../../../styles/booking.module.css';
import { toast } from 'react-toastify';
import { bookingService, roomServiceApi } from '../../../services/apiService';
import { format } from 'date-fns';
import Image from 'next/image';
import { FaPlus, FaTrash } from 'react-icons/fa';

interface BookingDetail {
  bookingId: number;
  customerId: number;
  customerName: string;
  employeeId: number;
  employeeName: string;
  bookingDate: string;
  checkInDate: string;
  checkOutDate: string;
  totalAmount: number;
  status: string;
  paymentStatus: string;
  notes: string;
  bookingSource: string;
  roomTypeId: number;
  roomTypeName: string;
  roomTypeQuantity: number;
  roomTypePrice: number;
  roomId?: number;
  roomNumber?: string;
  roomCheckInDate?: string;
  roomCheckOutDate?: string;
  roomStatus?: string;
}

interface BookingService {
  bookingServiceId: number;
  bookingId: number;
  serviceId: number;
  serviceName: string;
  quantity: number;
  price: number;
  serviceDate: string;
  status: string;
  notes: string;
}

interface RoomService {
  serviceId: number;
  name: string;
  price: number;
  description: string;
  isActive: boolean;
}

const BookingDetailPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const { } = useAuth();
  const [booking, setBooking] = useState<BookingDetail | null>(null);
  const [bookingServices, setBookingServices] = useState<BookingService[]>([]);
  const [availableServices, setAvailableServices] = useState<RoomService[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddServiceModalOpen, setIsAddServiceModalOpen] = useState(false);

  const [serviceFormData, setServiceFormData] = useState({
    serviceId: 0,
    quantity: 1,
    notes: ''
  });

  useEffect(() => {
    if (id) {
      fetchBookingDetail();
      fetchBookingServices();
      fetchAvailableServices();
    }
  }, [id]);

  const fetchBookingDetail = async () => {
    try {
      setLoading(true);
      // Sử dụng API service mới để lấy chi tiết đặt phòng
      const response = await bookingService.getBookingById(Number(id));

      if (response && response.success && response.data) {
        setBooking(response.data);
      } else {
        toast.error('Không thể tải thông tin đặt phòng');
        router.push('/bookings');
      }
    } catch (error: any) {
      console.error('Error fetching booking detail:', error);
      toast.error(error.message || 'Không thể tải thông tin đặt phòng');
      router.push('/bookings');
    } finally {
      setLoading(false);
    }
  };

  const fetchBookingServices = async () => {
    try {
      // Trong thực tế, nên có API riêng để lấy dịch vụ theo bookingId
      // Tạm thời để trống vì chưa có API
      setBookingServices([]);
    } catch (error: any) {
      console.error('Error fetching booking services:', error);
      toast.error(error.message || 'Không thể tải dịch vụ đặt phòng');
      setBookingServices([]);
    }
  };

  const fetchAvailableServices = async () => {
    try {
      // Sử dụng API service mới để lấy danh sách dịch vụ
      const response = await roomServiceApi.getAllServices();

      if (response && response.success && Array.isArray(response.data)) {
        // Lọc dịch vụ đang hoạt động
        const activeServices = response.data.filter((service: RoomService) => service.isActive);
        setAvailableServices(activeServices);
        if (activeServices.length > 0) {
          setServiceFormData(prev => ({
            ...prev,
            serviceId: activeServices[0].serviceId
          }));
        }
      } else {
        toast.error('Không thể tải danh sách dịch vụ: Dữ liệu không đúng định dạng');
        setAvailableServices([]);
      }
    } catch (error: any) {
      console.error('Error fetching available services:', error);
      toast.error(error.message || 'Không thể tải danh sách dịch vụ');
      setAvailableServices([]);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setServiceFormData(prev => ({
      ...prev,
      [name]: name === 'quantity' ? parseInt(value) : value
    }));
  };

  const handleAddService = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!booking) return;

    try {
      // Tìm thông tin dịch vụ từ danh sách dịch vụ có sẵn
      const selectedService = availableServices.find(s => s.serviceId === serviceFormData.serviceId);

      if (selectedService) {
        // Trong thực tế, nên có API riêng để thêm dịch vụ cho booking
        // Ở đây, chúng ta sẽ giả lập thành công và thêm dịch vụ vào state

        // Thêm dịch vụ mới vào state
        const newService: BookingService = {
          bookingServiceId: Math.floor(Math.random() * 1000) + 100, // ID ngẫu nhiên
          bookingId: booking.bookingId,
          serviceId: selectedService.serviceId,
          serviceName: selectedService.name,
          quantity: serviceFormData.quantity,
          price: selectedService.price,
          serviceDate: new Date().toISOString(),
          status: 'Confirmed',
          notes: serviceFormData.notes
        };

        setBookingServices(prev => [...prev, newService]);
        toast.success('Thêm dịch vụ thành công!');
      } else {
        toast.error('Không tìm thấy dịch vụ đã chọn');
      }

      setIsAddServiceModalOpen(false);
    } catch (error: any) {
      console.error('Error adding service:', error);
      toast.error(error.message || 'Thêm dịch vụ thất bại!');
    }
  };

  const handleDeleteService = async (bookingServiceId: number) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa dịch vụ này?')) {
      try {
        // Trong thực tế, nên có API riêng để xóa dịch vụ của booking
        // Ở đây, chúng ta sẽ giả lập thành công và xóa dịch vụ khỏi state

        // Xóa dịch vụ khỏi state
        setBookingServices(prev => prev.filter(service => service.bookingServiceId !== bookingServiceId));

        toast.success('Xóa dịch vụ thành công!');
      } catch (error: any) {
        console.error('Error deleting service:', error);
        toast.error(error.message || 'Xóa dịch vụ thất bại!');
      }
    }
  };

  const calculateTotalServiceAmount = () => {
    return bookingServices.reduce((total, service) => total + (service.price * service.quantity), 0);
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd/MM/yyyy');
  };

  const getStatusClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
      case 'completed':
        return styles.statusConfirmed;
      case 'pending':
        return styles.statusPending;
      case 'cancelled':
        return styles.statusCancelled;
      default:
        return styles.statusPending;
    }
  };

  const getPaymentStatusClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
        return styles.statusConfirmed;
      case 'unpaid':
        return styles.statusPending;
      case 'refunded':
        return styles.statusCancelled;
      default:
        return styles.statusPending;
    }
  };

  if (loading) {
    return (
      <>
        <Head>
          <title>Chi tiết đặt phòng | Hotel Nhóm 1</title>
        </Head>
        <Navbar />
        <div className={styles.pageContainer}>
          <div className={styles.loadingContainer}>
            <div className={styles.spinner}></div>
          </div>
        </div>
      </>
    );
  }

  if (!booking) {
    return (
      <>
        <Head>
          <title>Chi tiết đặt phòng | Hotel Nhóm 1</title>
        </Head>
        <Navbar />
        <div className={styles.pageContainer}>
          <div className={styles.errorMessage}>
            Không tìm thấy thông tin đặt phòng
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Chi tiết đặt phòng | Hotel Nhóm 1</title>
      </Head>
      <Navbar />
      <div className={styles.pageContainer}>
        <div className={styles.bookingDetailHeader}>
          <h1>Chi tiết đặt phòng #{booking.bookingId}</h1>
          <p>Xem thông tin chi tiết về đặt phòng của bạn</p>
        </div>

        <div className={styles.bookingDetailCard}>
          <div className={styles.bookingDetailSection}>
            <h2>Thông tin đặt phòng</h2>
            <div className={styles.bookingInfo}>
              <div className={styles.bookingInfoItem}>
                <span className={styles.label}>Mã đặt phòng:</span>
                <span className={styles.value}>#{booking.bookingId}</span>
              </div>
              <div className={styles.bookingInfoItem}>
                <span className={styles.label}>Ngày đặt:</span>
                <span className={styles.value}>{formatDate(booking.bookingDate)}</span>
              </div>
              <div className={styles.bookingInfoItem}>
                <span className={styles.label}>Trạng thái:</span>
                <span className={`${styles.value} ${styles.statusBadge} ${getStatusClass(booking.status)}`}>
                  {booking.status}
                </span>
              </div>
              <div className={styles.bookingInfoItem}>
                <span className={styles.label}>Thanh toán:</span>
                <span className={`${styles.value} ${styles.statusBadge} ${getPaymentStatusClass(booking.paymentStatus)}`}>
                  {booking.paymentStatus}
                </span>
              </div>
            </div>
          </div>

          <div className={styles.bookingDetailSection}>
            <h2>Thông tin khách hàng</h2>
            <div className={styles.bookingInfo}>
              <div className={styles.bookingInfoItem}>
                <span className={styles.label}>Họ tên:</span>
                <span className={styles.value}>{booking.customerName}</span>
              </div>
              <div className={styles.bookingInfoItem}>
                <span className={styles.label}>Ghi chú:</span>
                <span className={styles.value}>{booking.notes || 'Không có'}</span>
              </div>
            </div>
          </div>

          <div className={styles.bookingDetailSection}>
            <h2>Thông tin phòng</h2>
            <div className={styles.roomInfo}>
              <div className={styles.roomImageContainer}>
                <Image
                  src={`/rooms/${(booking.roomTypeName || 'standard').toLowerCase()}.jpg`}
                  alt={booking.roomTypeName || 'Room'}
                  width={300}
                  height={200}
                  objectFit="cover"
                  className={styles.roomImage}
                />
              </div>
              <div className={styles.roomDetails}>
                <h3>{booking.roomTypeName}</h3>
                <div className={styles.roomInfoItem}>
                  <span className={styles.label}>Số lượng:</span>
                  <span className={styles.value}>{booking.roomTypeQuantity}</span>
                </div>
                <div className={styles.roomInfoItem}>
                  <span className={styles.label}>Giá phòng:</span>
                  <span className={styles.value}>{booking.roomTypePrice.toLocaleString()} VND/đêm</span>
                </div>
                <div className={styles.roomInfoItem}>
                  <span className={styles.label}>Nhận phòng:</span>
                  <span className={styles.value}>{formatDate(booking.checkInDate)}</span>
                </div>
                <div className={styles.roomInfoItem}>
                  <span className={styles.label}>Trả phòng:</span>
                  <span className={styles.value}>{formatDate(booking.checkOutDate)}</span>
                </div>
                {booking.roomNumber && (
                  <div className={styles.roomInfoItem}>
                    <span className={styles.label}>Số phòng:</span>
                    <span className={styles.value}>{booking.roomNumber}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className={styles.bookingDetailSection}>
            <div className={styles.sectionHeader}>
              <h2>Dịch vụ đã đặt</h2>
              <button
                className={styles.addServiceButton}
                onClick={() => setIsAddServiceModalOpen(true)}
              >
                <FaPlus /> Thêm dịch vụ
              </button>
            </div>

            {bookingServices.length > 0 ? (
              <div className={styles.servicesTable}>
                <table>
                  <thead>
                    <tr>
                      <th>Dịch vụ</th>
                      <th>Số lượng</th>
                      <th>Đơn giá</th>
                      <th>Thành tiền</th>
                      <th>Ngày sử dụng</th>
                      <th>Trạng thái</th>
                      <th>Thao tác</th>
                    </tr>
                  </thead>
                  <tbody>
                    {bookingServices.map(service => (
                      <tr key={service.bookingServiceId}>
                        <td>{service.serviceName}</td>
                        <td>{service.quantity}</td>
                        <td>{service.price.toLocaleString()} VND</td>
                        <td>{(service.price * service.quantity).toLocaleString()} VND</td>
                        <td>{formatDate(service.serviceDate)}</td>
                        <td>
                          <span className={`${styles.statusBadge} ${getStatusClass(service.status)}`}>
                            {service.status}
                          </span>
                        </td>
                        <td>
                          <button
                            className={styles.deleteButton}
                            onClick={() => handleDeleteService(service.bookingServiceId)}
                          >
                            <FaTrash />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr>
                      <td colSpan={3}><strong>Tổng tiền dịch vụ:</strong></td>
                      <td colSpan={4}><strong>{calculateTotalServiceAmount().toLocaleString()} VND</strong></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            ) : (
              <div className={styles.emptyServices}>
                <p>Chưa có dịch vụ nào được đặt</p>
              </div>
            )}
          </div>

          <div className={styles.bookingDetailSection}>
            <h2>Tổng thanh toán</h2>
            <div className={styles.paymentSummary}>
              <div className={styles.paymentItem}>
                <span className={styles.label}>Tiền phòng:</span>
                <span className={styles.value}>{booking.totalAmount.toLocaleString()} VND</span>
              </div>
              <div className={styles.paymentItem}>
                <span className={styles.label}>Tiền dịch vụ:</span>
                <span className={styles.value}>{calculateTotalServiceAmount().toLocaleString()} VND</span>
              </div>
              <div className={`${styles.paymentItem} ${styles.totalAmount}`}>
                <span className={styles.label}>Tổng cộng:</span>
                <span className={styles.value}>{(booking.totalAmount + calculateTotalServiceAmount()).toLocaleString()} VND</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modal thêm dịch vụ */}
      {isAddServiceModalOpen && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h3>Thêm dịch vụ</h3>
              <button className={styles.closeButton} onClick={() => setIsAddServiceModalOpen(false)}>×</button>
            </div>
            <form onSubmit={handleAddService}>
              <div className={styles.formGroup}>
                <label>Dịch vụ</label>
                <select
                  name="serviceId"
                  value={serviceFormData.serviceId}
                  onChange={handleInputChange}
                  required
                >
                  {availableServices.map(service => (
                    <option key={service.serviceId} value={service.serviceId}>
                      {service.name} - {service.price.toLocaleString()} VND
                    </option>
                  ))}
                </select>
              </div>
              <div className={styles.formGroup}>
                <label>Số lượng</label>
                <input
                  type="number"
                  name="quantity"
                  min="1"
                  value={serviceFormData.quantity}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>Ghi chú</label>
                <textarea
                  name="notes"
                  value={serviceFormData.notes}
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>
              <div className={styles.modalFooter}>
                <button type="button" className={styles.cancelButton} onClick={() => setIsAddServiceModalOpen(false)}>
                  Hủy
                </button>
                <button type="submit" className={styles.submitButton}>
                  Thêm dịch vụ
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
};

export default BookingDetailPage;
