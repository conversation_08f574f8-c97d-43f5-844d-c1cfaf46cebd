namespace HOTELSV_BE.Models
{
    public class RoomModels
    {
        public int RoomId { get; set; }
        public string RoomNumber { get; set; } = string.Empty;
        public int RoomTypeId { get; set; }
        public string RoomType { get; set; } = string.Empty;
        public int Floor { get; set; }
        public string Status { get; set; } = string.Empty;
        public string CleaningStatus { get; set; } = string.Empty;
        public decimal BasePrice { get; set; }
        public int Capacity { get; set; }
        public string BedType { get; set; } = string.Empty;
        public string Amenities { get; set; } = string.Empty;
        public int TotalRecords { get; set; }
    }

    public class AddRoomModels
    {
        public string RoomNumber { get; set; } = string.Empty;
        public int RoomTypeId { get; set; }
        public int Floor { get; set; }
        public string CleaningStatus { get; set; } = string.Empty;
    }
    public class UpdateRoomModels
    {
        public string RoomNumber { get; set; } = string.Empty;
        public int RoomTypeId { get; set; }
        public int Floor { get; set; }
        public string Status { get; set; } = string.Empty;
        public string CleaningStatus { get; set; } = string.Empty;
    }

    public class RoomFilterRequest
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SearchTerm { get; set; }
        public int? RoomTypeId { get; set; }
        public string? Status { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public int? Floor { get; set; }
        public string? SortBy { get; set; }
        public bool IsAscending { get; set; } = true;
    }
}