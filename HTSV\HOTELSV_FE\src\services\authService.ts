import { authApi } from './api';

const authService = {
  async login(credentials: { email?: string; userName?: string; password: string }) {
    try {
      const response = await authApi.login(credentials);
      
      if (response.data.success && response.data.data.token) {
        localStorage.setItem('token', response.data.data.token);
        localStorage.setItem('user', JSON.stringify({
          id: response.data.data.userId,
          username: response.data.data.username,
          email: response.data.data.email,
          roles: response.data.data.roles,
          permissions: response.data.data.permissions
        }));
      }
      
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Đăng nhập thất bại');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async register(userData: any) {
    try {
      const response = await authApi.register(userData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || '<PERSON><PERSON>ng ký thất bại');
      }
      throw new Error('<PERSON>hông thể kết nối đến server');
    }
  },

  async logout() {
    try {
      const response = await authApi.logout();
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      return response.data;
    } catch (error: any) {
      // Xóa token và thông tin người dùng ngay cả khi API gọi thất bại
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      
      if (error.response) {
        throw new Error(error.response.data.message || 'Đăng xuất thất bại');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  getCurrentUser() {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      return JSON.parse(userStr);
    }
    return null;
  },

  isAuthenticated() {
    return !!localStorage.getItem('token');
  },

  hasPermission(permission: string) {
    const user = this.getCurrentUser();
    if (!user || !user.permissions) return false;
    return user.permissions.includes(permission);
  },

  hasRole(role: string) {
    const user = this.getCurrentUser();
    if (!user || !user.roles) return false;
    return user.roles.includes(role);
  }
};

export { authService };
