import React, { useState, useEffect } from 'react';
import { userApi } from '../../services/adminService';
import styles from '../../styles/admin.module.css';
import { FaEdit, FaTrash, FaSearch, FaEye } from 'react-icons/fa';
import { toast } from 'react-toastify';

interface User {
  userId: number;
  username: string;
  userName?: string; // Thêm trường này vì API có thể trả về userName thay vì username
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  isActive: boolean;
  createdDate: string;
  lastLogin: string | null;
  // Thêm các trường có thể có từ API
  fullName?: string;
  roleId?: number;
  roleName?: string;
}

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [showDetailModal, setShowDetailModal] = useState<boolean>(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const [formData, setFormData] = useState({
    username: '',
    password: '',
    email: '',
    firstName: '',
    lastName: '',
    phone: '',
    isActive: true
  });

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await userApi.getAllUsers();
      console.log('API Response:', response); // Debug log

      if (response.success) {
        // Kiểm tra xem response.data có phải là mảng không
        if (Array.isArray(response.data)) {
          setUsers(response.data);
        } else {
          console.error('Data is not an array:', response.data);
          // Nếu không phải mảng, kiểm tra xem có thuộc tính nào khác chứa mảng không
          if (response.data && typeof response.data === 'object') {
            const possibleArrays = Object.values(response.data).filter(val => Array.isArray(val));
            if (possibleArrays.length > 0) {
              setUsers(possibleArrays[0] as User[]);
            } else {
              toast.error('Không thể tải danh sách người dùng: Dữ liệu không đúng định dạng');
            }
          }
        }
      } else {
        toast.error('Không thể tải danh sách người dùng: Dữ liệu không đúng định dạng');
        setUsers([]);
      }
    } catch (error: any) {
      console.error('Error fetching users:', error);
      toast.error(error.message || 'Không thể tải danh sách người dùng');
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchUserDetail = async (id: number) => {
    try {
      const response = await userApi.getUserById(id);
      if (response.success) {
        setCurrentUser(response.data);
        setShowDetailModal(true);
      }
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentUser) return;

    try {
      const response = await userApi.updateUser(currentUser.userId, formData);
      if (response.success) {
        toast.success('Cập nhật người dùng thành công');
        fetchUsers();
        setShowModal(false);
      }
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const handleDeleteUser = async (id: number) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa người dùng này?')) {
      try {
        const response = await userApi.deleteUser(id);
        if (response.success) {
          toast.success('Xóa người dùng thành công');
          fetchUsers();
        }
      } catch (error: any) {
        toast.error(error.message);
      }
    }
  };

  const openEditModal = (user: User) => {
    setCurrentUser(user);
    setFormData({
      username: user.username || user.userName || '',
      password: '', // Password field is empty when editing
      email: user.email || '',
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      phone: user.phone || '',
      isActive: user.isActive
    });
    setShowModal(true);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Chưa đăng nhập';
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  };

  const filteredUsers = users.filter(user => {
    // Xử lý các trường có thể không tồn tại
    const username = user.username || user.userName || '';
    const email = user.email || '';
    const fullName = user.fullName || `${user.firstName || ''} ${user.lastName || ''}`.trim();

    const matchesSearch =
      username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fullName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' ||
      (statusFilter === 'active' && user.isActive) ||
      (statusFilter === 'inactive' && !user.isActive);

    return matchesSearch && matchesStatus;
  });

  return (
    <div>
      <div className={styles.toolbarSection}>
        <div className={styles.searchBox}>
          <FaSearch />
          <input
            type="text"
            placeholder="Tìm kiếm người dùng..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className={styles.filterContainer}>
          <select
            className={styles.filterSelect}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">Tất cả trạng thái</option>
            <option value="active">Đang hoạt động</option>
            <option value="inactive">Không hoạt động</option>
          </select>
        </div>
      </div>

      {loading ? (
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
        </div>
      ) : (
        <div className={styles.tableContainer}>
          <table className={styles.dataTable}>
            <thead>
              <tr>
                <th>ID</th>
                <th>Tên đăng nhập</th>
                <th>Email</th>
                <th>Họ tên</th>
                <th>Điện thoại</th>
                <th>Trạng thái</th>
                <th>Ngày tạo</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.length > 0 ? (
                filteredUsers.map((user) => (
                  <tr key={user.userId}>
                    <td>{user.userId}</td>
                    <td>{user.username || user.userName || 'N/A'}</td>
                    <td>{user.email || 'N/A'}</td>
                    <td>{user.fullName || `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'N/A'}</td>
                    <td>{user.phone || 'N/A'}</td>
                    <td>
                      <span className={`${styles.statusBadge} ${user.isActive ? styles.statusAvailable : styles.statusBooked}`}>
                        {user.isActive ? 'Hoạt động' : 'Không hoạt động'}
                      </span>
                    </td>
                    <td>{formatDate(user.createdDate)}</td>
                    <td>
                      <button
                        className={`${styles.actionButton} ${styles.viewButton}`}
                        onClick={() => fetchUserDetail(user.userId)}
                      >
                        <FaEye />
                      </button>
                      <button
                        className={`${styles.actionButton} ${styles.editButton}`}
                        onClick={() => openEditModal(user)}
                      >
                        <FaEdit />
                      </button>
                      <button
                        className={`${styles.actionButton} ${styles.deleteButton}`}
                        onClick={() => handleDeleteUser(user.userId)}
                      >
                        <FaTrash />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={8} style={{ textAlign: 'center' }}>
                    Không tìm thấy người dùng nào
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* Modal for editing user */}
      {showModal && currentUser && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h2 className={styles.modalTitle}>
                Chỉnh sửa người dùng
              </h2>
              <button className={styles.closeButton} onClick={() => setShowModal(false)}>
                &times;
              </button>
            </div>
            <form onSubmit={handleUpdateUser}>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>Tên đăng nhập</label>
                  <input
                    type="text"
                    name="username"
                    value={formData.username}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>Email</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
              <div className={styles.formGroup}>
                <label>Mật khẩu (để trống nếu không thay đổi)</label>
                <input
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                />
              </div>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>Họ</label>
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>Tên</label>
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
              <div className={styles.formGroup}>
                <label>Điện thoại</label>
                <input
                  type="text"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                />
              </div>
              <div className={styles.formGroup}>
                <label>
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                  />
                  Đang hoạt động
                </label>
              </div>
              <div className={styles.buttonContainer}>
                <button
                  type="button"
                  className={`${styles.button} ${styles.secondaryButton}`}
                  onClick={() => setShowModal(false)}
                >
                  Hủy
                </button>
                <button type="submit" className={`${styles.button} ${styles.primaryButton}`}>
                  Cập nhật
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal for user details */}
      {showDetailModal && currentUser && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h2 className={styles.modalTitle}>
                Chi tiết người dùng
              </h2>
              <button className={styles.closeButton} onClick={() => setShowDetailModal(false)}>
                &times;
              </button>
            </div>
            <div className={styles.formContainer}>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>ID</label>
                  <p>{currentUser.userId}</p>
                </div>
                <div className={styles.formGroup}>
                  <label>Tên đăng nhập</label>
                  <p>{currentUser.username || currentUser.userName || 'N/A'}</p>
                </div>
              </div>
              <div className={styles.formGroup}>
                <label>Email</label>
                <p>{currentUser.email || 'N/A'}</p>
              </div>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>Họ</label>
                  <p>{currentUser.firstName || 'N/A'}</p>
                </div>
                <div className={styles.formGroup}>
                  <label>Tên</label>
                  <p>{currentUser.lastName || 'N/A'}</p>
                </div>
              </div>
              <div className={styles.formGroup}>
                <label>Điện thoại</label>
                <p>{currentUser.phone || 'Không có'}</p>
              </div>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>Trạng thái</label>
                  <p>{currentUser.isActive ? 'Đang hoạt động' : 'Không hoạt động'}</p>
                </div>
                <div className={styles.formGroup}>
                  <label>Ngày tạo</label>
                  <p>{formatDate(currentUser.createdDate)}</p>
                </div>
              </div>
              <div className={styles.formGroup}>
                <label>Lần đăng nhập cuối</label>
                <p>{formatDate(currentUser.lastLogin)}</p>
              </div>
            </div>
            <div className={styles.buttonContainer}>
              <button
                className={`${styles.button} ${styles.secondaryButton}`}
                onClick={() => setShowDetailModal(false)}
              >
                Đóng
              </button>
              <button
                className={`${styles.button} ${styles.primaryButton}`}
                onClick={() => {
                  setShowDetailModal(false);
                  openEditModal(currentUser);
                }}
              >
                Chỉnh sửa
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;
