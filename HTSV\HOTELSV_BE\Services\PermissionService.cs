using System.Data.SqlClient;
using Dapper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using HOTELSV_BE.Models;

namespace HOTELSV_BE.Services
{
    internal class UserPermissionDto
    {
        public int UserId { get; set; }
        public string? RoleName { get; set; }
        public string? PermissionName { get; set; }
    }

    public interface IPermissionService
    {
        Task<UserPermission> GetUserPermissions(int userId);
        Task<bool> HasPermission(int userId, string permissionName);
    }

    public class PermissionService : IPermissionService
    {
        private readonly string _connectionString;
        private readonly ILogger<PermissionService> _logger;

        public PermissionService(IConfiguration configuration, ILogger<PermissionService> logger)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") ?? 
                throw new ArgumentNullException("DefaultConnection string is not configured");
            _logger = logger;
        }

        public async Task<UserPermission> GetUserPermissions(int userId)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                const string sql = @"
                    SELECT DISTINCT u.UserId, r.Role<PERSON>ame, p.PermissionName 
                    FROM Users u
                    JOIN UserRoles ur ON u.UserId = ur.UserId
                    JOIN Roles r ON ur.RoleId = r.RoleId
                    JOIN RolePermissions rp ON r.RoleId = rp.RoleId
                    JOIN Permissions p ON rp.PermissionId = p.PermissionId
                    WHERE u.UserId = @UserId";

                var result = await connection.QueryAsync<UserPermissionDto>(sql, new { UserId = userId });
                var permissionList = result.ToList();

                if (!permissionList.Any())
                {
                    _logger.LogWarning("No permissions found for userId: {UserId}", userId);
                    return new UserPermission { UserId = userId };
                }

                return new UserPermission
                {
                    UserId = userId,
                    Roles = permissionList
                        .Where(r => r.RoleName != null)
                        .Select(r => r.RoleName!)
                        .Distinct()
                        .ToList(),
                    Permissions = permissionList
                        .Where(r => r.PermissionName != null)
                        .Select(r => r.PermissionName!)
                        .Distinct()
                        .ToList()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permissions for userId: {UserId}", userId);
                throw;
            }
        }

        public async Task<bool> HasPermission(int userId, string permissionName)
        {
            try
            {
                var userPermissions = await GetUserPermissions(userId);
                return userPermissions.Permissions.Contains(permissionName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking permission {Permission} for userId: {UserId}", 
                    permissionName, userId);
                return false;
            }
        }
    }
}
