.pageContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.servicesHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.servicesHeader h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.servicesHeader p {
  font-size: 1.1rem;
  color: #666;
}

.filterTabs {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filterTab {
  background-color: transparent;
  border: none;
  padding: 0.8rem 1.5rem;
  margin: 0 0.5rem 0.5rem 0;
  font-size: 1rem;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
  text-transform: capitalize;
}

.filterTab:hover {
  background-color: #f5f5f5;
}

.filterTab.active {
  background-color: #3498db;
  color: white;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #3498db;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.emptyState {
  text-align: center;
  padding: 3rem;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.emptyState h2 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 1rem;
}

.emptyState p {
  font-size: 1.1rem;
  color: #666;
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.serviceCard {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
}

.serviceCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.serviceImageContainer {
  position: relative;
  height: 200px;
}

.serviceImage {
  border-radius: 8px 8px 0 0;
}

.serviceCategory {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.categoryBadge {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  text-transform: capitalize;
}

.serviceContent {
  padding: 1.5rem;
}

.serviceName {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 0.8rem;
}

.serviceDescription {
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.serviceFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
}

.priceInfo {
  display: flex;
  flex-direction: column;
}

.priceValue {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
}

.bookButton {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.bookButton:hover {
  background-color: #2980b9;
}

@media (max-width: 768px) {
  .servicesGrid {
    grid-template-columns: 1fr;
  }
}
