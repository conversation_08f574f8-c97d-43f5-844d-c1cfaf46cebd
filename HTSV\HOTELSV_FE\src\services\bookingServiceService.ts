import { bookingServiceApi } from './api';

const bookingServiceService = {
  async getAllBookingServices() {
    try {
      const response = await bookingServiceApi.getAllBookingServices();
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách dịch vụ đặt phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getBookingServiceById(id: number) {
    try {
      const response = await bookingServiceApi.getBookingServiceById(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải thông tin dịch vụ đặt phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async addBookingService(bookingServiceData: any) {
    try {
      const response = await bookingServiceApi.addBookingService(bookingServiceData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể thêm dịch vụ đặt phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async updateBookingService(id: number, bookingServiceData: any) {
    try {
      const response = await bookingServiceApi.updateBookingService(id, bookingServiceData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể cập nhật dịch vụ đặt phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async deleteBookingService(id: number) {
    try {
      const response = await bookingServiceApi.deleteBookingService(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể xóa dịch vụ đặt phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getBookingServicesPaginated(filter: any) {
    try {
      const response = await bookingServiceApi.getBookingServicesPaginated(filter);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách dịch vụ đặt phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  }
};

export { bookingServiceService };