import { customerApi } from './api';

const customerService = {
  async getAllCustomers() {
    try {
      const response = await customerApi.getAllCustomers();
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách khách hàng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getCustomerById(id: number) {
    try {
      const response = await customerApi.getCustomerById(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải thông tin khách hàng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async addCustomer(customerData: any) {
    try {
      const response = await customerApi.addCustomer(customerData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể thêm khách hàng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async updateCustomer(id: number, customerData: any) {
    try {
      const response = await customerApi.updateCustomer(id, customerData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể cập nhật khách hàng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async deleteCustomer(id: number) {
    try {
      const response = await customerApi.deleteCustomer(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể xóa khách hàng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getCustomersPaginated(filter: any) {
    try {
      const response = await customerApi.getCustomersPaginated(filter);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách khách hàng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  }
};

export { customerService };