﻿USE [data_QLKS_113_Nhom1]
GO
/****** Object:  StoredProcedure [dbo].[sp_AddPermission]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[sp_AddPermission]
    @Description nvarchar(200)
AS
BEGIN
    INSERT INTO Permissions (Description)
    VALUES (@Description);
    
    SELECT SCOPE_IDENTITY() AS PermissionId;
END;
GO
/****** Object:  StoredProcedure [dbo].[sp_AddRole]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[sp_AddRole]
    @RoleName nvarchar(50),
    @Description nvarchar(200)
AS
BEGIN
    INSERT INTO Roles (RoleName, Description)
    VALUES (@RoleName, @Description);
    
    SELECT SCOPE_IDENTITY() AS RoleId;
END;
GO
/****** Object:  StoredProcedure [dbo].[sp_AddRolePermission]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[sp_AddRolePermission]
    @RoleId int,
    @PermissionId int
AS
BEGIN
    INSERT INTO RolePermissions (RoleId, PermissionId)
    VALUES (@RoleId, @PermissionId);
    
    SELECT SCOPE_IDENTITY() AS RolePermissionId;
END;
GO
/****** Object:  StoredProcedure [dbo].[sp_AddUserRole]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[sp_AddUserRole]
    @UserId int,
    @RoleId int
AS
BEGIN
    INSERT INTO UserRoles (UserId, RoleId)
    VALUES (@UserId, @RoleId);
    
    SELECT SCOPE_IDENTITY() AS UserRoleId;
END;

GO
/****** Object:  StoredProcedure [dbo].[sp_DeletePermission]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sp_DeletePermission]
    @PermissionId int
AS
BEGIN
    DELETE FROM RolePermissions WHERE PermissionId = @PermissionId;
    DELETE FROM Permissions WHERE PermissionId = @PermissionId;
END;
GO
/****** Object:  StoredProcedure [dbo].[sp_DeleteRole]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[sp_DeleteRole]
    @RoleId int
AS
BEGIN
    DELETE FROM UserRoles WHERE RoleId = @RoleId;
    DELETE FROM RolePermissions WHERE RoleId = @RoleId;
    DELETE FROM Roles WHERE RoleId = @RoleId;
END;
GO
/****** Object:  StoredProcedure [dbo].[sp_DeleteRolePermission]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sp_DeleteRolePermission]
    @RolePermissionId int
AS
BEGIN
    DELETE FROM RolePermissions WHERE RolePermissionId = @RolePermissionId;
END;
GO
/****** Object:  StoredProcedure [dbo].[sp_DeleteUserRole]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sp_DeleteUserRole]
    @UserRoleId int
AS
BEGIN
    DELETE FROM UserRoles WHERE UserRoleId = @UserRoleId;
END;

GO
/****** Object:  StoredProcedure [dbo].[sp_GetAllPermissions]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sp_GetAllPermissions]
AS
BEGIN
    SELECT * FROM Permissions;
END;
GO
/****** Object:  StoredProcedure [dbo].[sp_GetAllRolePermissions]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[sp_GetAllRolePermissions]
AS
BEGIN
    SELECT rp.*, r.RoleName, p.Description as PermissionDescription
    FROM RolePermissions rp
    JOIN Roles r ON rp.RoleId = r.RoleId
    JOIN Permissions p ON rp.PermissionId = p.PermissionId;
END;
GO
/****** Object:  StoredProcedure [dbo].[sp_GetAllRoles]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[sp_GetAllRoles]
AS
BEGIN
    SELECT * FROM Roles;
END;
GO
/****** Object:  StoredProcedure [dbo].[sp_GetAllUserRoles]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sp_GetAllUserRoles]
AS
BEGIN
    SELECT ur.*, u.Username, r.RoleName
    FROM UserRoles ur
    JOIN Users u ON ur.UserId = u.UserId
    JOIN Roles r ON ur.RoleId = r.RoleId;
END;

GO
/****** Object:  StoredProcedure [dbo].[sp_GetPermissionById]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sp_GetPermissionById]
    @PermissionId int
AS
BEGIN
    SELECT * FROM Permissions WHERE PermissionId = @PermissionId;
END;
GO
/****** Object:  StoredProcedure [dbo].[sp_GetRoleById]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sp_GetRoleById]
    @RoleId int
AS
BEGIN
    SELECT * FROM Roles WHERE RoleId = @RoleId;
END;
GO
/****** Object:  StoredProcedure [dbo].[sp_GetRolePermissionById]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sp_GetRolePermissionById]
    @RolePermissionId int
AS
BEGIN
    SELECT rp.*, r.RoleName, p.Description as PermissionDescription
    FROM RolePermissions rp
    JOIN Roles r ON rp.RoleId = r.RoleId
    JOIN Permissions p ON rp.PermissionId = p.PermissionId
    WHERE rp.RolePermissionId = @RolePermissionId;
END;
GO
/****** Object:  StoredProcedure [dbo].[sp_GetUserRoleById]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sp_GetUserRoleById]
    @UserRoleId int
AS
BEGIN
    SELECT ur.*, u.Username, r.RoleName
    FROM UserRoles ur
    JOIN Users u ON ur.UserId = u.UserId
    JOIN Roles r ON ur.RoleId = r.RoleId
    WHERE ur.UserRoleId = @UserRoleId;
END;

GO
/****** Object:  StoredProcedure [dbo].[sp_UpdatePermission]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:      <Nhóm 1 ( Tuấn đẹp trai vãi)>
-- =============================================

CREATE PROCEDURE [dbo].[sp_UpdatePermission]
    @PermissionId INT,
    @PermissionName NVARCHAR(100) = NULL,
    @Description NVARCHAR(200) = NULL
AS
BEGIN
    BEGIN TRY
        BEGIN TRANSACTION;
        
        UPDATE Permissions
        SET PermissionName = ISNULL(@PermissionName, PermissionName),
            Description = ISNULL(@Description, Description)
        WHERE PermissionId = @PermissionId;
        
        -- Trả về thông tin quyền đã cập nhật
        SELECT PermissionId, PermissionName, Description
        FROM Permissions
        WHERE PermissionId = @PermissionId;
        
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        SELECT ERROR_MESSAGE() AS ErrorMessage;
    END CATCH
END
GO
/****** Object:  StoredProcedure [dbo].[sp_UpdateRole]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- Author:      <Nhóm 1 ( Tuấn đẹp trai vãi)>
-- =============================================
CREATE PROCEDURE [dbo].[sp_UpdateRole]
    @RoleId INT,
    @RoleName NVARCHAR(50) = NULL,
    @Description NVARCHAR(200) = NULL
AS
BEGIN
    BEGIN TRY
        BEGIN TRANSACTION;
        
        UPDATE Roles
        SET RoleName = ISNULL(@RoleName, RoleName),
            Description = ISNULL(@Description, Description)
        WHERE RoleId = @RoleId;
        
        -- Trả về thông tin vai trò đã cập nhật
        SELECT RoleId, RoleName, Description
        FROM Roles
        WHERE RoleId = @RoleId;
        
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        SELECT ERROR_MESSAGE() AS ErrorMessage;
    END CATCH
END
GO
/****** Object:  StoredProcedure [dbo].[sp_UpdateRolePermission]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:      <Nhóm 1 ( Tuấn đẹp trai vãi)>
-- =============================================

CREATE PROCEDURE [dbo].[sp_UpdateRolePermission]
    @RolePermissionId INT,
    @RoleId INT = NULL,
    @PermissionId INT = NULL
AS
BEGIN
    BEGIN TRY
        BEGIN TRANSACTION;
        
        UPDATE RolePermissions
        SET RoleId = ISNULL(@RoleId, RoleId),
            PermissionId = ISNULL(@PermissionId, PermissionId)
        WHERE RolePermissionId = @RolePermissionId;
        
        -- Trả về thông tin vai trò-quyền đã cập nhật
        SELECT rp.RolePermissionId, rp.RoleId, rp.PermissionId,
               r.RoleName, p.PermissionName
        FROM RolePermissions rp
        LEFT JOIN Roles r ON rp.RoleId = r.RoleId
        LEFT JOIN Permissions p ON rp.PermissionId = p.PermissionId
        WHERE rp.RolePermissionId = @RolePermissionId;
        
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        SELECT ERROR_MESSAGE() AS ErrorMessage;
    END CATCH
END
GO
/****** Object:  StoredProcedure [dbo].[sp_UpdateUserRole]    Script Date: 5/21/2025 11:29:19 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- Author:      <Nhóm 1 ( Tuấn đẹp trai vãi)>
-- =============================================
CREATE PROCEDURE [dbo].[sp_UpdateUserRole]
    @UserRoleId INT,
    @UserId INT = NULL,
    @RoleId INT = NULL
AS
BEGIN
    BEGIN TRY
        BEGIN TRANSACTION;
        
        UPDATE UserRoles
        SET UserId = ISNULL(@UserId, UserId),
            RoleId = ISNULL(@RoleId, RoleId)
        WHERE UserRoleId = @UserRoleId;
        
        -- Trả về thông tin người dùng-vai trò đã cập nhật
        SELECT ur.UserRoleId, ur.UserId, ur.RoleId,
               u.Username, u.FirstName + ' ' + u.LastName AS UserName,
               r.RoleName
        FROM UserRoles ur
        LEFT JOIN Users u ON ur.UserId = u.UserId
        LEFT JOIN Roles r ON ur.RoleId = r.RoleId
        WHERE ur.UserRoleId = @UserRoleId;
        
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        SELECT ERROR_MESSAGE() AS ErrorMessage;
    END CATCH
END
GO
