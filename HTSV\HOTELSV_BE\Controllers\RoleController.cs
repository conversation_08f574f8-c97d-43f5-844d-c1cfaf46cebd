using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using HOTELSV_BE.Models;
using System.Data.SqlClient;
using Dapper;

namespace HOTELSV_BE.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class RoleController : ControllerBase
    {
        private readonly string _connectionString;

        public RoleController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        [HttpPost("Add")]
        [Authorize(Roles = "Administrator")]
        public async Task<ActionResult<AddRoleResponse>> AddRole([FromBody] AddRoleRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new 
                    { 
                        RoleName = request.RoleName,
                        Description = request.Description 
                    };
                    
                    var roleId = await connection.QueryFirstOrDefaultAsync<int>(
                        "sp_AddRole",
                        parameters,
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    return Ok(new AddRoleResponse
                    {
                        Success = true,
                        Message = "Role added successfully",
                        RoleId = roleId
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new AddRoleResponse
                {
                    Success = false,
                    Message = $"Error adding role: {ex.Message}"
                });
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Administrator")]
        public async Task<ActionResult<DeleteRoleResponse>> DeleteRole(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.ExecuteAsync(
                        "sp_DeleteRole",
                        new { RoleId = id },
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    return Ok(new DeleteRoleResponse
                    {
                        Success = true,
                        Message = "Role deleted successfully"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new DeleteRoleResponse
                {
                    Success = false,
                    Message = $"Error deleting role: {ex.Message}"
                });
            }
        }

        [HttpPut("Update")]
        [Authorize(Roles = "Administrator")]
        public async Task<ActionResult<UpdateRoleResponse>> UpdateRole([FromBody] UpdateRoleRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new 
                    { 
                        RoleId = request.RoleId,
                        RoleName = request.RoleName,
                        Description = request.Description
                    };

                    var updatedRole = await connection.QueryFirstOrDefaultAsync<Role>(
                        "sp_UpdateRole",
                        parameters,
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    if (updatedRole == null)
                    {
                        return NotFound(new UpdateRoleResponse
                        {
                            Success = false,
                            Message = "Role not found"
                        });
                    }

                    return Ok(new UpdateRoleResponse
                    {
                        Success = true,
                        Message = "Role updated successfully",
                        Role = updatedRole
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new UpdateRoleResponse
                {
                    Success = false,
                    Message = $"Error updating role: {ex.Message}"
                });
            }
        }

        [HttpGet]
        [Authorize]
        public async Task<ActionResult<GetAllRolesResponse>> GetAllRoles()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var roles = await connection.QueryAsync<Role>(
                        "sp_GetAllRoles",
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    return Ok(new GetAllRolesResponse
                    {
                        Success = true,
                        Message = "Roles retrieved successfully",
                        Roles = roles.ToList()
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new GetAllRolesResponse
                {
                    Success = false,
                    Message = $"Error retrieving roles: {ex.Message}"
                });
            }
        }

        [HttpGet("{id}")]
        [Authorize]
        public async Task<ActionResult<GetRoleResponse>> GetRoleById(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var role = await connection.QueryFirstOrDefaultAsync<Role>(
                        "sp_GetRoleById",
                        new { RoleId = id },
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    if (role == null)
                    {
                        return NotFound(new GetRoleResponse
                        {
                            Success = false,
                            Message = "Role not found"
                        });
                    }

                    return Ok(new GetRoleResponse
                    {
                        Success = true,
                        Message = "Role retrieved successfully",
                        Role = role
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new GetRoleResponse
                {
                    Success = false,
                    Message = $"Error retrieving role: {ex.Message}"
                });
            }
        }
    }
}
