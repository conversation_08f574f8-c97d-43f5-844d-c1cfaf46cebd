import React, { ReactNode, useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import Sidebar from './Sidebar';
import styles from '../../styles/admin.module.css';
import { toast } from 'react-toastify';

interface AdminLayoutProps {
  children: ReactNode;
  title: string;
  activeTab: string;
  setActiveTab?: (tab: string) => void;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children, title, activeTab, setActiveTab }) => {
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();
  const [currentTab, setCurrentTabState] = useState(activeTab);

  // Sử dụng setActiveTab từ props nếu có, nếu không sử dụng state nội bộ
  const handleSetActiveTab = (tab: string) => {
    if (setActiveTab) {
      setActiveTab(tab);
    } else {
      setCurrentTabState(tab);
    }
  };

  // Tạm thời bỏ qua kiểm tra quyền admin để phát triển
  const skipAdminCheck = true;

  useEffect(() => {
    if (!isAuthenticated) {
      toast.error('Vui lòng đăng nhập để truy cập trang quản trị');
      router.push('/auth/login');
      return;
    }

    // Kiểm tra quyền admin - Bỏ qua nếu skipAdminCheck = true
    if (!skipAdminCheck && user && user.roleId !== 1) {
      toast.error('Bạn không có quyền truy cập trang quản trị');
      router.push('/');
    }
  }, [isAuthenticated, user, router]);

  // Hiển thị loading hoặc null trong khi kiểm tra xác thực
  if (!isAuthenticated || !user) {
    return null;
  }

  // Kiểm tra quyền admin - Bỏ qua nếu skipAdminCheck = true
  if (!skipAdminCheck && user.roleId !== 1) {
    return null;
  }

  return (
    <>
      <Head>
        <title>{title} | Hotel Nhóm 1</title>
      </Head>
      <div className={styles.adminContainer}>
        <Sidebar activeTab={activeTab} setActiveTab={handleSetActiveTab} />
        <main className={styles.mainContent}>
          <div className={styles.header}>
            <h1>{title}</h1>
            <div className={styles.userInfo}>
              <span>Xin chào, {user?.userName || 'Admin'}</span>
            </div>
          </div>
          <div className={styles.contentArea}>
            {children}
          </div>
        </main>
      </div>
    </>
  );
};

export default AdminLayout;
