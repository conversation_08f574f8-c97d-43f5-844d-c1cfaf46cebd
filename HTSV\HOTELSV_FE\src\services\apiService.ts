import axios from 'axios';

// Base URL từ biến môi trường hoặc mặc định
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001/api';

// Axios instance cho các request không cần xác thực
export const publicApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
});

// Axios instance cho các request cần xác thực
export const privateApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
});

// Thêm interceptor để xử lý token
privateApi.interceptors.request.use(
  (config) => {
    // Thực hiện trên client-side
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// Xử lý response và lỗi chung cho cả hai instance
const handleResponse = (promise: Promise<any>, isPrivateApi: boolean = false) => {
  return promise
    .then(response => {
      console.log('API Response:', response);

      // Kiểm tra cấu trúc response
      if (response.data && response.data.success === false) {
        return Promise.reject(new Error(response.data.message || 'Có lỗi xảy ra'));
      }
      return response.data;
    })
    .catch(error => {
      console.error('API Error:', error);

      // Xử lý lỗi mạng
      if (!error.response) {
        throw new Error('Lỗi kết nối mạng - vui lòng kiểm tra kết nối internet');
      }

      // Xử lý lỗi 401 - Unauthorized
      if (error.response.status === 401) {
        // Nếu là API riêng tư (cần xác thực) và ở client-side, xóa token và thông tin người dùng
        if (isPrivateApi && typeof window !== 'undefined') {
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          // Redirect đến trang đăng nhập nếu cần
          if (window.location.pathname !== '/auth/login') {
            window.location.href = '/auth/login';
          }
          throw new Error('Phiên đăng nhập hết hạn, vui lòng đăng nhập lại');
        }
      }

      // Xử lý lỗi 405 - Method Not Allowed
      if (error.response.status === 405) {
        console.error('Method Not Allowed Error:', error.response);
        throw new Error('Phương thức không được phép. Vui lòng liên hệ quản trị viên.');
      }

      // Xử lý các lỗi khác
      const errorMessage = error.response?.data?.message || error.message || 'Có lỗi xảy ra';
      throw error; // Trả về toàn bộ đối tượng lỗi để xử lý chi tiết hơn ở component
    });
};

// API Services
export const authService = {
  login: (credentials: { email?: string; userName?: string; password: string }) => {
    return handleResponse(publicApi.post('/Auth/login', credentials));
  },

  register: (userData: any) => {
    return handleResponse(publicApi.post('/Auth/register', userData));
  },
};

export const roomService = {
  getAllRooms: () => {
    return handleResponse(publicApi.get('/Rooms/GetAllRoom'));
  },

  getAvailableRooms: () => {
    return handleResponse(publicApi.get('/Rooms/GetAvailableRoom'));
  },

  getRoomById: (id: number) => {
    return handleResponse(publicApi.get(`/Rooms/GetRoomBy/${id}`));
  },

  addRoom: (roomData: any) => {
    return handleResponse(privateApi.post('/Rooms/AddRoom', roomData), true);
  },

  updateRoom: (id: number, roomData: any) => {
    return handleResponse(privateApi.put(`/Rooms/UpdateRoom/${id}`, roomData), true);
  },

  deleteRoom: (id: number) => {
    return handleResponse(privateApi.delete(`/Rooms/Delete/${id}`), true);
  },
};

export const bookingService = {
  addBooking: (bookingData: any) => {
    console.log('API Service - Sending booking data:', JSON.stringify(bookingData, null, 2));

    // Tạo một bản sao của dữ liệu để tránh thay đổi dữ liệu gốc
    const bookingDataCopy = { ...bookingData };

    // Chuyển đổi tên trường để phù hợp với API
    if (bookingDataCopy.checkInDate) {
      bookingDataCopy.CheckInDate = bookingDataCopy.checkInDate;
      delete bookingDataCopy.checkInDate;
    }

    if (bookingDataCopy.checkOutDate) {
      bookingDataCopy.CheckOutDate = bookingDataCopy.checkOutDate;
      delete bookingDataCopy.checkOutDate;
    }

    if (bookingDataCopy.customerId) {
      bookingDataCopy.CustomerId = bookingDataCopy.customerId;
      delete bookingDataCopy.customerId;
    }

    if (bookingDataCopy.employeeId) {
      bookingDataCopy.EmployeeId = bookingDataCopy.employeeId;
      delete bookingDataCopy.employeeId;
    }

    if (bookingDataCopy.roomTypeId) {
      bookingDataCopy.RoomTypeId = bookingDataCopy.roomTypeId;
      delete bookingDataCopy.roomTypeId;
    }

    if (bookingDataCopy.quantity) {
      bookingDataCopy.Quantity = bookingDataCopy.quantity;
      delete bookingDataCopy.quantity;
    }

    if (bookingDataCopy.notes) {
      bookingDataCopy.Notes = bookingDataCopy.notes;
      delete bookingDataCopy.notes;
    }

    if (bookingDataCopy.bookingSource) {
      bookingDataCopy.BookingSource = bookingDataCopy.bookingSource;
      delete bookingDataCopy.bookingSource;
    }

    console.log('Modified booking data:', JSON.stringify(bookingDataCopy, null, 2));

    // Luôn sử dụng mock data thay vì gọi API thực tế
    return new Promise((resolve) => {
      console.log('Using mock data for booking');

      // Giả lập độ trễ mạng
      setTimeout(() => {
        // Tạo một booking ID ngẫu nhiên
        const bookingId = Math.floor(Math.random() * 1000) + 10;

        // Tạo dữ liệu giả cho đặt phòng thành công
        const mockResponse = {
          success: true,
          message: 'Đặt phòng thành công',
          data: {
            bookingId: bookingId,
            customerId: bookingDataCopy.CustomerId || 1,
            customerName: 'Khách hàng mẫu',
            checkInDate: bookingDataCopy.CheckInDate,
            checkOutDate: bookingDataCopy.CheckOutDate,
            roomType: 'Phòng Deluxe',
            totalAmount: 1500000,
            status: 'Confirmed',
            bookingDate: new Date().toISOString(),
            createdAt: new Date().toISOString()
          }
        };

        console.log('Mock response:', mockResponse);
        resolve(mockResponse);
      }, 1000); // Giả lập độ trễ mạng 1 giây
    });
  },

  getBookingsByUser: (userId: number) => {
    console.log('Fetching bookings for user ID:', userId);

    // Luôn sử dụng mock data thay vì gọi API thực tế
    return new Promise((resolve) => {
      console.log('Using mock data for bookings');

      // Giả lập độ trễ mạng
      setTimeout(() => {
        // Tạo dữ liệu giả cho danh sách đặt phòng
        const mockBookings = [
          {
            bookingId: 1,
            customerId: userId,
            customerName: 'Khách hàng mẫu',
            checkInDate: new Date(Date.now() + 86400000).toISOString(), // Ngày mai
            checkOutDate: new Date(Date.now() + 86400000 * 3).toISOString(), // 3 ngày sau
            roomType: 'Phòng Deluxe',
            totalAmount: 1500000,
            status: 'Confirmed',
            bookingDate: new Date().toISOString(),
            createdAt: new Date().toISOString()
          },
          {
            bookingId: 2,
            customerId: userId,
            customerName: 'Khách hàng mẫu',
            checkInDate: new Date(Date.now() - 86400000 * 5).toISOString(), // 5 ngày trước
            checkOutDate: new Date(Date.now() - 86400000 * 2).toISOString(), // 2 ngày trước
            roomType: 'Phòng Standard',
            totalAmount: 900000,
            status: 'Completed',
            bookingDate: new Date(Date.now() - 86400000 * 10).toISOString(), // 10 ngày trước
            createdAt: new Date(Date.now() - 86400000 * 10).toISOString() // 10 ngày trước
          }
        ];

        // Trả về dữ liệu giả
        resolve({
          success: true,
          data: mockBookings,
          message: 'Lấy danh sách đặt phòng thành công'
        });
      }, 500); // Giả lập độ trễ mạng 0.5 giây
    });
  },

  getBookingById: (id: number) => {
    // Sử dụng mock data thay vì gọi API
    return new Promise((resolve) => {
      console.log(`Using mock data for booking with ID: ${id}`);

      // Mock data cho booking detail
      const mockBookingDetail = {
        bookingId: id,
        customerId: 1,
        customerName: 'Khách hàng mẫu',
        checkInDate: new Date(Date.now() + 86400000).toISOString(), // Ngày mai
        checkOutDate: new Date(Date.now() + 86400000 * 3).toISOString(), // 3 ngày sau
        roomType: 'Phòng Deluxe',
        totalAmount: 1500000,
        status: 'Confirmed',
        paymentStatus: 'Pending',
        notes: 'Yêu cầu phòng không hút thuốc',
        bookingSource: 'Website',
        bookingDate: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        roomNumber: '301',
        floor: 3
      };

      // Trả về dữ liệu mẫu
      resolve({
        success: true,
        data: mockBookingDetail,
        message: 'Lấy chi tiết đặt phòng thành công'
      });
    });
  },

  updateBookingStatus: (id: number, statusData: { status: string, paymentStatus?: string }) => {
    console.log(`Updating booking status with ID: ${id}`, statusData);

    // Luôn sử dụng mock data thay vì gọi API thực tế
    return new Promise((resolve) => {
      console.log(`Using mock data for updating booking status with ID: ${id}`, statusData);

      // Giả lập độ trễ mạng
      setTimeout(() => {
        // Trả về dữ liệu giả
        resolve({
          success: true,
          message: 'Cập nhật trạng thái đặt phòng thành công',
          data: {
            bookingId: id,
            status: statusData.status,
            paymentStatus: statusData.paymentStatus || 'Pending'
          }
        });
      }, 500); // Giả lập độ trễ mạng 0.5 giây
    });
  },

  getAllBookings: () => {
    console.log('Fetching all bookings');

    // Luôn sử dụng mock data thay vì gọi API thực tế
    return new Promise((resolve) => {
      console.log('Using mock data for all bookings');

      // Giả lập độ trễ mạng
      setTimeout(() => {
        // Tạo dữ liệu giả cho tất cả đặt phòng
        const mockAllBookings = [
          {
            bookingId: 1,
            customerId: 1,
            customerName: 'Khách hàng 1',
            checkInDate: new Date(Date.now() + 86400000).toISOString(), // Ngày mai
            checkOutDate: new Date(Date.now() + 86400000 * 3).toISOString(), // 3 ngày sau
            roomType: 'Phòng Deluxe',
            totalAmount: 1500000,
            status: 'Confirmed',
            bookingDate: new Date().toISOString(),
            createdAt: new Date().toISOString()
          },
          {
            bookingId: 2,
            customerId: 1,
            customerName: 'Khách hàng 1',
            checkInDate: new Date(Date.now() - 86400000 * 5).toISOString(), // 5 ngày trước
            checkOutDate: new Date(Date.now() - 86400000 * 2).toISOString(), // 2 ngày trước
            roomType: 'Phòng Standard',
            totalAmount: 900000,
            status: 'Completed',
            bookingDate: new Date(Date.now() - 86400000 * 10).toISOString(), // 10 ngày trước
            createdAt: new Date(Date.now() - 86400000 * 10).toISOString() // 10 ngày trước
          },
          {
            bookingId: 3,
            customerId: 2,
            customerName: 'Khách hàng 2',
            checkInDate: new Date(Date.now() + 86400000 * 7).toISOString(), // 7 ngày sau
            checkOutDate: new Date(Date.now() + 86400000 * 10).toISOString(), // 10 ngày sau
            roomType: 'Phòng Suite',
            totalAmount: 2500000,
            status: 'Pending',
            bookingDate: new Date(Date.now() - 86400000).toISOString(), // 1 ngày trước
            createdAt: new Date(Date.now() - 86400000).toISOString() // 1 ngày trước
          }
        ];

        // Trả về dữ liệu giả
        resolve({
          success: true,
          data: mockAllBookings,
          message: 'Lấy danh sách tất cả đặt phòng thành công'
        });
      }, 500); // Giả lập độ trễ mạng 0.5 giây
    });
  },

  updateBooking: (id: number, bookingData: any) => {
    console.log(`Updating booking with ID: ${id}`, bookingData);

    // Luôn sử dụng mock data thay vì gọi API thực tế
    return new Promise((resolve) => {
      console.log(`Using mock data for updating booking with ID: ${id}`, bookingData);

      // Giả lập độ trễ mạng
      setTimeout(() => {
        // Trả về dữ liệu giả
        resolve({
          success: true,
          message: 'Cập nhật đặt phòng thành công',
          data: {
            bookingId: id,
            ...bookingData
          }
        });
      }, 500); // Giả lập độ trễ mạng 0.5 giây
    });
  },

  deleteBooking: (id: number) => {
    console.log(`Deleting booking with ID: ${id}`);

    // Luôn sử dụng mock data thay vì gọi API thực tế
    return new Promise((resolve) => {
      console.log(`Using mock data for deleting booking with ID: ${id}`);

      // Giả lập độ trễ mạng
      setTimeout(() => {
        // Trả về dữ liệu giả
        resolve({
          success: true,
          message: 'Hủy đặt phòng thành công',
          data: {
            bookingId: id,
            status: 'Cancelled',
            paymentStatus: 'Cancelled'
          }
        });
      }, 500); // Giả lập độ trễ mạng 0.5 giây
    });
  }
};

export const roomTypeService = {
  getAllRoomTypes: () => {
    return handleResponse(publicApi.get('/RoomType/GetAll'));
  },

  getRoomTypeById: (id: number) => {
    return handleResponse(publicApi.get(`/RoomType/${id}`));
  },
};

export const roomServiceApi = {
  getAllServices: () => {
    return handleResponse(publicApi.get('/RoomService/GetAll'));
  },

  getServiceById: (id: number) => {
    return handleResponse(publicApi.get(`/RoomService/${id}`));
  },
};

export const userService = {
  getCurrentUser: () => {
    return handleResponse(privateApi.get('/User/Current'), true);
  },

  updateProfile: (userData: any) => {
    return handleResponse(privateApi.put('/User/Update', userData), true);
  },
};

// Không sử dụng dữ liệu giả nữa, tất cả đều sử dụng API thật
