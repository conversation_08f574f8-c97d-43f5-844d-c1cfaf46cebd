namespace HOTELSV_BE.Models
{
    public class Permission
    {
        public int PermissionId { get; set; }
        public string PermissionName { get; set; }
        public string Description { get; set; }
    }

    public class AddPermissionRequest
    {
        public string PermissionName { get; set; }
        public string Description { get; set; }
    }

    public class AddPermissionResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public int PermissionId { get; set; }
    }

    public class DeletePermissionResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
    }

    public class GetPermissionResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public Permission Permission { get; set; }
    }

    public class GetAllPermissionsResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public List<Permission> Permissions { get; set; }
    }

    public class UpdatePermissionRequest
    {
        public int PermissionId { get; set; }
        public string? PermissionName { get; set; }
        public string? Description { get; set; }
    }
    public class UpdatePermissionResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public Permission? Permission { get; set; }
    }
}
