import Head from 'next/head';
import Navbar from '../components/Navbar';
import styles from '../styles/contact.module.css';

export default function Contact() {
  return (
    <>
      <Head>
        <title>Liên hệ | Hotel Nhóm 1</title>
      </Head>
      <Navbar />
      <div className={styles.contactContainer}>
        <div className={styles.contactHeader}>
          <h1>Liên hệ với chúng tôi</h1>
          <p>Chúng tôi luôn sẵn sàng lắng nghe ý kiến của bạn</p>
        </div>
        
        <div className={styles.contactContent}>
          <div className={styles.contactInfo}>
            <div className={styles.infoItem}>
              <h3>Địa chỉ</h3>
              <p>123 Đường ABC, Quận XYZ, TP.HCM</p>
            </div>
            <div className={styles.infoItem}>
              <h3>Email</h3>
              <p><EMAIL></p>
            </div>
            <div className={styles.infoItem}>
              <h3>Điện thoại</h3>
              <p>0123 456 789</p>
            </div>
          </div>
          
          <form className={styles.contactForm}>
            <div className={styles.formGroup}>
              <label>Họ tên</label>
              <input type="text" placeholder="Nhập họ tên của bạn" />
            </div>
            <div className={styles.formGroup}>
              <label>Email</label>
              <input type="email" placeholder="Nhập email của bạn" />
            </div>
            <div className={styles.formGroup}>
              <label>Tin nhắn</label>
              <textarea placeholder="Nhập nội dung tin nhắn" rows={5}></textarea>
            </div>
            <button type="submit" className={styles.submitButton}>
              Gửi tin nhắn
            </button>
          </form>
        </div>
      </div>
    </>
  );
}
