import { invoiceApi } from './api';

const invoiceService = {
  async getAllInvoices() {
    try {
      const response = await invoiceApi.getAllInvoices();
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách hóa đơn');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getInvoiceById(id: number) {
    try {
      const response = await invoiceApi.getInvoiceById(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải thông tin hóa đơn');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async addInvoice(invoiceData: any) {
    try {
      const response = await invoiceApi.addInvoice(invoiceData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể thêm hóa đơn');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async updateInvoice(id: number, invoiceData: any) {
    try {
      const response = await invoiceApi.updateInvoice(id, invoiceData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể cập nhật hóa đơn');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async deleteInvoice(id: number) {
    try {
      const response = await invoiceApi.deleteInvoice(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể xóa hóa đơn');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getInvoicesPaginated(filter: any) {
    try {
      const response = await invoiceApi.getInvoicesPaginated(filter);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách hóa đơn');
      }
      throw new Error('Không thể kết nối đến server');
    }
  }
};

export { invoiceService };