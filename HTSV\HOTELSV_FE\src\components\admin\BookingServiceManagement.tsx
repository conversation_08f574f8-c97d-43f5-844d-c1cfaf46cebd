import React, { useState, useEffect } from 'react';
import { bookingServiceApi, bookingApi, roomServiceApi } from '../../services/adminService';
import styles from '../../styles/admin.module.css';
import { FaEdit, FaTrash, FaSearch, FaPlus } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { format } from 'date-fns';

interface BookingService {
  bookingServiceId: number;
  bookingId: number;
  serviceId: number;
  serviceName: string;
  quantity: number;
  price: number;
  serviceDate: string;
  status: string;
  notes: string;
  requestedBy?: number;
  servedBy?: number;
}

interface Booking {
  bookingId: number;
  customerName: string;
}

interface RoomService {
  serviceId: number;
  name: string;
  price: number;
}

const BookingServiceManagement = () => {
  const [bookingServices, setBookingServices] = useState<BookingService[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [services, setServices] = useState<RoomService[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedBookingService, setSelectedBookingService] = useState<BookingService | null>(null);
  
  const [formData, setFormData] = useState({
    bookingId: 0,
    serviceId: 0,
    quantity: 1,
    serviceDate: format(new Date(), 'yyyy-MM-dd'),
    notes: '',
    requestedBy: 1
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [bookingServicesRes, bookingsRes, servicesRes] = await Promise.all([
        bookingServiceApi.getAllBookingServices(),
        bookingApi.getAllBookings(),
        roomServiceApi.getAllRoomServices()
      ]);
      
      if (bookingServicesRes.success) {
        setBookingServices(bookingServicesRes.data);
      }
      
      if (bookingsRes.success) {
        setBookings(bookingsRes.data);
      }
      
      if (servicesRes.success) {
        setServices(servicesRes.data);
      }
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const openAddModal = () => {
    setFormData({
      bookingId: bookings.length > 0 ? bookings[0].bookingId : 0,
      serviceId: services.length > 0 ? services[0].serviceId : 0,
      quantity: 1,
      serviceDate: format(new Date(), 'yyyy-MM-dd'),
      notes: '',
      requestedBy: 1
    });
    setIsAddModalOpen(true);
  };

  const openEditModal = (bookingService: BookingService) => {
    setSelectedBookingService(bookingService);
    setFormData({
      bookingId: bookingService.bookingId,
      serviceId: bookingService.serviceId,
      quantity: bookingService.quantity,
      serviceDate: format(new Date(bookingService.serviceDate), 'yyyy-MM-dd'),
      notes: bookingService.notes,
      requestedBy: bookingService.requestedBy || 1
    });
    setIsEditModalOpen(true);
  };

  const handleAddBookingService = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await bookingServiceApi.addBookingService(formData);
      if (response.success) {
        toast.success('Thêm dịch vụ đặt phòng thành công!');
        fetchData();
        setIsAddModalOpen(false);
      } else {
        toast.error(response.message || 'Thêm dịch vụ đặt phòng thất bại!');
      }
    } catch (error: any) {
      toast.error(error.message || 'Thêm dịch vụ đặt phòng thất bại!');
    }
  };

  const handleUpdateBookingService = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedBookingService) return;
    
    try {
      const response = await bookingServiceApi.updateBookingService(selectedBookingService.bookingServiceId, formData);
      if (response.success) {
        toast.success('Cập nhật dịch vụ đặt phòng thành công!');
        fetchData();
        setIsEditModalOpen(false);
      } else {
        toast.error(response.message || 'Cập nhật dịch vụ đặt phòng thất bại!');
      }
    } catch (error: any) {
      toast.error(error.message || 'Cập nhật dịch vụ đặt phòng thất bại!');
    }
  };

  const handleDeleteBookingService = async (bookingServiceId: number) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa dịch vụ đặt phòng này?')) {
      try {
        const response = await bookingServiceApi.deleteBookingService(bookingServiceId);
        if (response.success) {
          toast.success('Xóa dịch vụ đặt phòng thành công!');
          fetchData();
        } else {
          toast.error(response.message || 'Xóa dịch vụ đặt phòng thất bại!');
        }
      } catch (error: any) {
        toast.error(error.message || 'Xóa dịch vụ đặt phòng thất bại!');
      }
    }
  };

  const getBookingName = (bookingId: number) => {
    const booking = bookings.find(b => b.bookingId === bookingId);
    return booking ? `#${bookingId} - ${booking.customerName}` : `#${bookingId}`;
  };

  const getServiceName = (serviceId: number) => {
    const service = services.find(s => s.serviceId === serviceId);
    return service ? service.name : `Dịch vụ #${serviceId}`;
  };

  const filteredBookingServices = bookingServices.filter(bs => 
    getBookingName(bs.bookingId).toLowerCase().includes(searchTerm.toLowerCase()) ||
    bs.serviceName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    bs.status.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div>
      <div className={styles.toolbarSection}>
        <div className={styles.searchBox}>
          <FaSearch />
          <input
            type="text"
            placeholder="Tìm kiếm dịch vụ đặt phòng..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <button className={`${styles.button} ${styles.primaryButton}`} onClick={openAddModal}>
          <FaPlus /> Thêm dịch vụ đặt phòng
        </button>
      </div>

      {loading ? (
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
        </div>
      ) : (
        <div className={styles.tableContainer}>
          <table className={styles.dataTable}>
            <thead>
              <tr>
                <th>ID</th>
                <th>Đặt phòng</th>
                <th>Dịch vụ</th>
                <th>Số lượng</th>
                <th>Giá</th>
                <th>Ngày sử dụng</th>
                <th>Trạng thái</th>
                <th>Ghi chú</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {filteredBookingServices.length > 0 ? (
                filteredBookingServices.map((bs) => (
                  <tr key={bs.bookingServiceId}>
                    <td>{bs.bookingServiceId}</td>
                    <td>{getBookingName(bs.bookingId)}</td>
                    <td>{bs.serviceName}</td>
                    <td>{bs.quantity}</td>
                    <td>{bs.price.toLocaleString()} VND</td>
                    <td>{format(new Date(bs.serviceDate), 'dd/MM/yyyy')}</td>
                    <td>
                      <span className={`${styles.statusBadge} ${bs.status === 'Completed' ? styles.statusAvailable : styles.statusPending}`}>
                        {bs.status}
                      </span>
                    </td>
                    <td>{bs.notes.length > 20 ? `${bs.notes.substring(0, 20)}...` : bs.notes}</td>
                    <td>
                      <button
                        className={`${styles.actionButton} ${styles.editButton}`}
                        onClick={() => openEditModal(bs)}
                      >
                        <FaEdit />
                      </button>
                      <button
                        className={`${styles.actionButton} ${styles.deleteButton}`}
                        onClick={() => handleDeleteBookingService(bs.bookingServiceId)}
                      >
                        <FaTrash />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={9} style={{ textAlign: 'center' }}>
                    Không tìm thấy dịch vụ đặt phòng nào
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* Modal thêm dịch vụ đặt phòng */}
      {isAddModalOpen && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h3>Thêm dịch vụ đặt phòng mới</h3>
              <button className={styles.closeButton} onClick={() => setIsAddModalOpen(false)}>×</button>
            </div>
            <form onSubmit={handleAddBookingService}>
              <div className={styles.formGroup}>
                <label>Đặt phòng</label>
                <select
                  name="bookingId"
                  value={formData.bookingId}
                  onChange={handleInputChange}
                  required
                >
                  {bookings.map(booking => (
                    <option key={booking.bookingId} value={booking.bookingId}>
                      #{booking.bookingId} - {booking.customerName}
                    </option>
                  ))}
                </select>
              </div>
              <div className={styles.formGroup}>
                <label>Dịch vụ</label>
                <select
                  name="serviceId"
                  value={formData.serviceId}
                  onChange={handleInputChange}
                  required
                >
                  {services.map(service => (
                    <option key={service.serviceId} value={service.serviceId}>
                      {service.name} - {service.price.toLocaleString()} VND
                    </option>
                  ))}
                </select>
              </div>
              <div className={styles.formGroup}>
                <label>Số lượng</label>
                <input
                  type="number"
                  name="quantity"
                  min="1"
                  value={formData.quantity}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>Ngày sử dụng</label>
                <input
                  type="date"
                  name="serviceDate"
                  value={formData.serviceDate}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>Ghi chú</label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>
              <div className={styles.modalFooter}>
                <button type="button" className={styles.cancelButton} onClick={() => setIsAddModalOpen(false)}>
                  Hủy
                </button>
                <button type="submit" className={styles.submitButton}>
                  Thêm dịch vụ
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal chỉnh sửa dịch vụ đặt phòng */}
      {isEditModalOpen && selectedBookingService && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h3>Chỉnh sửa dịch vụ đặt phòng</h3>
              <button className={styles.closeButton} onClick={() => setIsEditModalOpen(false)}>×</button>
            </div>
            <form onSubmit={handleUpdateBookingService}>
              <div className={styles.formGroup}>
                <label>Đặt phòng</label>
                <select
                  name="bookingId"
                  value={formData.bookingId}
                  onChange={handleInputChange}
                  required
                >
                  {bookings.map(booking => (
                    <option key={booking.bookingId} value={booking.bookingId}>
                      #{booking.bookingId} - {booking.customerName}
                    </option>
                  ))}
                </select>
              </div>
              <div className={styles.formGroup}>
                <label>Dịch vụ</label>
                <select
                  name="serviceId"
                  value={formData.serviceId}
                  onChange={handleInputChange}
                  required
                >
                  {services.map(service => (
                    <option key={service.serviceId} value={service.serviceId}>
                      {service.name} - {service.price.toLocaleString()} VND
                    </option>
                  ))}
                </select>
              </div>
              <div className={styles.formGroup}>
                <label>Số lượng</label>
                <input
                  type="number"
                  name="quantity"
                  min="1"
                  value={formData.quantity}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>Ngày sử dụng</label>
                <input
                  type="date"
                  name="serviceDate"
                  value={formData.serviceDate}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>Ghi chú</label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>
              <div className={styles.modalFooter}>
                <button type="button" className={styles.cancelButton} onClick={() => setIsEditModalOpen(false)}>
                  Hủy
                </button>
                <button type="submit" className={styles.submitButton}>
                  Cập nhật
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default BookingServiceManagement;
