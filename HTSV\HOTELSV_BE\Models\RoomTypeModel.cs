public class RoomType
{
    public int RoomTypeId { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public decimal BasePrice { get; set; }
    public int Capacity { get; set; }
    public string BedType { get; set; }
    public string Amenities { get; set; }
    public bool IsActive { get; set; }
    public int TotalRecords { get; set; }
}

public class AddRoomType
{
    public string Name { get; set; }
    public string Description { get; set; }
    public decimal BasePrice { get; set; }
    public int Capacity { get; set; }
    public string BedType { get; set; }
    public string Amenities { get; set; }
    public bool IsActive { get; set; }
}

public class RoomTypeFilterRequest
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SearchTerm { get; set; }
    public decimal? MinPrice { get; set; }
    public decimal? MaxPrice { get; set; }
    public int? Capacity { get; set; }
    public string? BedType { get; set; }
    public string? SortBy { get; set; }
    public bool IsAscending { get; set; } = true;
}