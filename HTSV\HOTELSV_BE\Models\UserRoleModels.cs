namespace HOTELSV_BE.Models
{
    public class UserRole
    {
        public int UserRoleId { get; set; }
        public int UserId { get; set; }
        public int RoleId { get; set; }
        public string Username { get; set; } = string.Empty;
        public string RoleName { get; set; } = string.Empty;
    }

    public class AddUserRoleRequest
    {
        public int UserId { get; set; }
        public int RoleId { get; set; }
    }

    public class AddUserRoleResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int UserRoleId { get; set; }
    }

    public class UpdateUserRoleRequest
    {
        public int UserRoleId { get; set; }
        public int? UserId { get; set; }
        public int? RoleId { get; set; }
    }

    public class UpdateUserRoleResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public UserRole? UserRole { get; set; }
    }

    public class DeleteUserRoleResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class GetUserRoleResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public UserRole? UserRole { get; set; }
    }

    public class GetAllUserRolesResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<UserRole> UserRoles { get; set; } = new();
    }
}
