using Microsoft.EntityFrameworkCore;
using Data.Models;
using System;
using System.Data.SqlClient;

namespace Data
{
    public class DBConnect
    {
        private readonly string _connectionString;

        public DBConnect(string connectionString)
        {
            _connectionString = connectionString;
        }

        public HotelDbContext CreateDbContext()
        {
            var optionsBuilder = new DbContextOptionsBuilder<HotelDbContext>();
            optionsBuilder.UseSqlServer(_connectionString);
            return new HotelDbContext(optionsBuilder.Options);
        }

        // Phương thức kiểm tra kết nối
        public bool TestConnection()
        {
            try
            {
                using (var context = CreateDbContext())
                {
                    // Thử kết nối đến database
                    context.Database.OpenConnection();
                    context.Database.CloseConnection();
                    return true;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }
    }

    public class HotelDbContext : DbContext
    {
        public HotelDbContext(DbContextOptions<HotelDbContext> options) : base(options)
        {
        }

        // Định nghĩa các DbSet cho các bảng
        public DbSet<User> Users { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            
            // Cấu hình các entity ở đây
        }
    }
} 