import { roomTypeApi } from './api';

const roomTypeService = {
  async getAllRoomTypes() {
    try {
      const response = await roomTypeApi.getAllRoomTypes();
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách loại phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getRoomTypeById(id: number) {
    try {
      const response = await roomTypeApi.getRoomTypeById(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải thông tin loại phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async addRoomType(roomTypeData: any) {
    try {
      const response = await roomTypeApi.addRoomType(roomTypeData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể thêm loại phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async updateRoomType(id: number, roomTypeData: any) {
    try {
      const response = await roomTypeApi.updateRoomType(id, roomTypeData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể cập nhật loại phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async deleteRoomType(id: number) {
    try {
      const response = await roomTypeApi.deleteRoomType(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể xóa loại phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getRoomTypesPaginated(filter: any) {
    try {
      const response = await roomTypeApi.getRoomTypesPaginated(filter);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách loại phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  }
};

export { roomTypeService };