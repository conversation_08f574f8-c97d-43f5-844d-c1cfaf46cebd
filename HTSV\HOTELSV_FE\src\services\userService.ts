import { userApi } from './api';

const userService = {
  async getAllUsers() {
    try {
      const response = await userApi.getAllUsers();
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || '<PERSON>hông thể tải danh sách người dùng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getUserById(id: number) {
    try {
      const response = await userApi.getUserById(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải thông tin người dùng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async addUser(userData: any) {
    try {
      const response = await userApi.addUser(userData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || '<PERSON>hô<PERSON> thể thêm người dùng');
      }
      throw new Error('<PERSON>hông thể kết nối đến server');
    }
  },

  async updateUser(id: number, userData: any) {
    try {
      const response = await userApi.updateUser(id, userData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể cập nhật người dùng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async deleteUser(id: number) {
    try {
      const response = await userApi.deleteUser(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể xóa người dùng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getUsersPaginated(filter: any) {
    try {
      const response = await userApi.getUsersPaginated(filter);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách người dùng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  }
};

export { userService };