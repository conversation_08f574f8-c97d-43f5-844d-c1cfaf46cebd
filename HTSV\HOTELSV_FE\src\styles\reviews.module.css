.reviewsContainer {
  padding-top: 80px;
  min-height: 100vh;
  background: linear-gradient(to bottom, var(--background-color), #0f0f0f);
}

.reviewsHeader {
  text-align: center;
  padding: 4rem 0;
  background: rgba(255, 255, 255, 0.03);
  position: relative;
  overflow: hidden;
}

.reviewsHeader::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/hinhanhtrangchu/anhbackgroundtrangchu.png') center/cover;
  opacity: 0.1;
  z-index: -1;
}

.reviewsHeader h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, var(--primary-color), #D4AF37);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: fadeInDown 0.8s ease;
}

.reviewsList {
  max-width: 1200px;
  margin: 3rem auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.reviewCard {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  animation: fadeIn 0.5s ease;
}

.reviewCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.08);
}

.reviewCard p {
  color: #fff;
  line-height: 1.6;
}

.reviewHeader {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.reviewHeader p {
  color: #fff;
}

.avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 1rem;
}

.rating {
  color: var(--primary-color);
  margin: 0.5rem 0;
}

.star {
  color: var(--primary-color);
  font-size: 1.2rem;
}

.reviewInfo h3 {
  color: var(--primary-color);
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
}

.writeReview {
  max-width: 800px;
  margin: 4rem auto;
  padding: 3rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.writeReview h2 {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  background: linear-gradient(45deg, var(--primary-color), #D4AF37);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.reviewForm {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formGroup label {
  color: #fff;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.formGroup input,
.formGroup textarea,
.formGroup select {
  width: 100%;
  color: #fff;
  background: rgba(255, 255, 255, 0.07);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.formGroup select option {
  background-color: #121212;
  color: white;
}

.formGroup input::placeholder,
.formGroup textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.formGroup input:focus,
.formGroup textarea:focus,
.formGroup select:focus {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--primary-color);
  outline: none;
}

.formGroup textarea {
  resize: vertical;
}

.submitButton {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submitButton:hover {
  background: rgba(192, 160, 128, 0.8);
}
