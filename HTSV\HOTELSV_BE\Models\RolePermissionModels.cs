namespace HOTELSV_BE.Models
{
    public class RolePermission
    {
        public int RolePermissionId { get; set; }
        public int RoleId { get; set; }
        public int PermissionId { get; set; }
        public string RoleName { get; set; } = string.Empty;
        public string PermissionDescription { get; set; } = string.Empty;
    }

    public class AddRolePermissionRequest
    {
        public int RoleId { get; set; }
        public int PermissionId { get; set; }
    }

    public class AddRolePermissionResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int RolePermissionId { get; set; }
    }

    public class UpdateRolePermissionRequest
    {
        public int RolePermissionId { get; set; }
        public int? RoleId { get; set; }
        public int? PermissionId { get; set; }
    }

    public class UpdateRolePermissionResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public RolePermission? RolePermission { get; set; }
    }

    public class DeleteRolePermissionResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class GetRolePermissionResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public RolePermission? RolePermission { get; set; }
    }

    public class GetAllRolePermissionsResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<RolePermission> RolePermissions { get; set; } = new();
    }
}
