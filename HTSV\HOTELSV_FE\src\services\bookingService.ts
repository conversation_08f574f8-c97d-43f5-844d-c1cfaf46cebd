import { bookingApi } from './api';

const bookingService = {
  async addBooking(bookingData: any) {
    try {
      // Chuyển đổi tên trường để phù hợp với API
      const formattedData = {
        CustomerId: bookingData.customerId || bookingData.CustomerId,
        EmployeeId: bookingData.employeeId || bookingData.EmployeeId,
        CheckInDate: bookingData.checkInDate || bookingData.CheckInDate,
        CheckOutDate: bookingData.checkOutDate || bookingData.CheckOutDate,
        RoomTypeId: bookingData.roomTypeId || bookingData.RoomTypeId,
        Quantity: bookingData.quantity || bookingData.Quantity || 1,
        Notes: bookingData.notes || bookingData.Notes || '',
        BookingSource: bookingData.bookingSource || bookingData.BookingSource || 'Website'
      };

      const response = await bookingApi.addBooking(formattedData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể đặt phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async updateBooking(id: number, bookingData: any) {
    try {
      // Chuyển đổi tên trường để phù hợp với API
      const formattedData = {
        CustomerId: bookingData.customerId || bookingData.CustomerId,
        EmployeeId: bookingData.employeeId || bookingData.EmployeeId,
        CheckInDate: bookingData.checkInDate || bookingData.CheckInDate,
        CheckOutDate: bookingData.checkOutDate || bookingData.CheckOutDate,
        TotalAmount: bookingData.totalAmount || bookingData.TotalAmount,
        Status: bookingData.status || bookingData.Status,
        PaymentStatus: bookingData.paymentStatus || bookingData.PaymentStatus,
        Notes: bookingData.notes || bookingData.Notes,
        BookingSource: bookingData.bookingSource || bookingData.BookingSource
      };

      const response = await bookingApi.updateBooking(id, formattedData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể cập nhật đặt phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async updateBookingStatus(id: number, statusData: { status: string, paymentStatus?: string }) {
    try {
      // Không cần chuyển đổi tên trường vì API đã mong đợi các trường với tên viết thường
      const formattedData = {
        status: statusData.status,
        paymentStatus: statusData.paymentStatus
      };

      const response = await bookingApi.updateBookingStatus(id, formattedData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể cập nhật trạng thái đặt phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async deleteBooking(id: number) {
    try {
      const response = await bookingApi.deleteBooking(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể xóa đặt phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getBookingDetails(id: number) {
    try {
      const response = await bookingApi.getBookingDetails(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải thông tin đặt phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getAllBookings() {
    try {
      const response = await bookingApi.getAllBookings();
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách đặt phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getBookingsPaginated(filter: any) {
    try {
      const response = await bookingApi.getBookingsPaginated(filter);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách đặt phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  }
};

export { bookingService };