import Head from 'next/head';
import Navbar from '../components/Navbar';
import ProtectedRoute from '../components/ProtectedRoute';
import styles from '../styles/reviews.module.css';

const reviews = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>ăn <PERSON>',
    rating: 5,
    comment: 'Dịch vụ tuyệt vời, phòng ốc sạch sẽ và nhân viên rất thân thiện',
    date: '2024-01-15',
    avatar: '/avatars/user1.jpg'
  },
  // Thêm các đánh giá khác
];

const ReviewsPage = () => {
  return (
    <ProtectedRoute>
      <>
        <Head>
          <title>Đánh giá | Hotel Nhóm 1</title>
        </Head>
        <Navbar />
        <div className={styles.reviewsContainer}>
          <div className={styles.reviewsHeader}>
            <h1>Đánh giá từ khách hàng</h1>
            <p>Những tr<PERSON>i nghiệm thực tế từ khách hàng của chúng tôi</p>
          </div>
          
          <div className={styles.reviewsList}>
            {reviews.map(review => (
              <div key={review.id} className={styles.reviewCard}>
                <div className={styles.reviewHeader}>
                  <img src={review.avatar} alt={review.name} className={styles.avatar} />
                  <div className={styles.reviewInfo}>
                    <h3>{review.name}</h3>
                    <div className={styles.rating}>
                      {[...Array(review.rating)].map((_, i) => (
                        <span key={i} className={styles.star}>★</span>
                      ))}
                    </div>
                    <span className={styles.date}>{review.date}</span>
                  </div>
                </div>
                <p className={styles.comment}>{review.comment}</p>
              </div>
            ))}
          </div>
          
          <div className={styles.writeReview}>
            <h2>Viết đánh giá</h2>
            <form className={styles.reviewForm}>
              <div className={styles.formGroup}>
                <label>Đánh giá của bạn</label>
                <div className={styles.ratingInput}>
                  {[1, 2, 3, 4, 5].map((num) => (
                    <button key={num} type="button" className={styles.ratingButton}>
                      ★
                    </button>
                  ))}
                </div>
              </div>
              <div className={styles.formGroup}>
                <label>Nhận xét</label>
                <textarea placeholder="Chia sẻ trải nghiệm của bạn" rows={4}></textarea>
              </div>
              <button type="submit" className={styles.submitButton}>
                Gửi đánh giá
              </button>
            </form>
          </div>
        </div>
      </>
    </ProtectedRoute>
  );
};

export default ReviewsPage;
