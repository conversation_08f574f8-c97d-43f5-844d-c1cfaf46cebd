import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import Sidebar from '../../components/admin/Sidebar';
import BookingServiceManagement from '../../components/admin/BookingServiceManagement';
import styles from '../../styles/admin.module.css';
import { toast } from 'react-toastify';

export default function BookingServicesPage() {
  const [activeTab, setActiveTab] = useState('bookingServices');
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Chỉ kiểm tra đăng nhập, không kiểm tra quyền admin
    if (!isAuthenticated) {
      toast.error('Vui lòng đăng nhập để truy cập trang quản trị');
      // Sử dụng setTimeout để tránh vấn đề với Next.js
      setTimeout(() => {
        router.push('/auth/login');
      }, 100);
    }
  }, [isAuthenticated, router]);

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Quản lý dịch vụ đặt phòng | Hotel Nhóm 1</title>
      </Head>
      <div className={styles.adminContainer}>
        <Sidebar activeTab={activeTab} setActiveTab={setActiveTab} />
        <main className={styles.mainContent}>
          <div className={styles.header}>
            <h1>Quản lý dịch vụ đặt phòng</h1>
            <div className={styles.userInfo}>
              <span>Xin chào, {user?.userName || 'Admin'}</span>
            </div>
          </div>
          <div className={styles.contentArea}>
            <BookingServiceManagement />
          </div>
        </main>
      </div>
    </>
  );
}
