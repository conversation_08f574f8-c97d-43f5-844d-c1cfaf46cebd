using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using System.Security.Claims;
using HOTELSV_BE.Services;

namespace HOTELSV_BE.Attributes
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class RequirePermissionAttribute : TypeFilterAttribute
    {
        public RequirePermissionAttribute(string permission) 
            : base(typeof(PermissionAuthorizationFilter))
        {
            Arguments = new object[] { permission };
        }
    }

    public class PermissionAuthorizationFilter : IAsyncAuthorizationFilter
    {
        private readonly string _permission;
        private readonly IPermissionService _permissionService;
        private readonly ILogger<PermissionAuthorizationFilter> _logger;

        public PermissionAuthorizationFilter(
            string permission, 
            IPermissionService permissionService,
            ILogger<PermissionAuthorizationFilter> logger)
        {
            _permission = permission;
            _permissionService = permissionService;
            _logger = logger;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            try
            {
                var userIdClaim = context.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || string.IsNullOrEmpty(userIdClaim.Value))
                {
                    _logger.LogWarning("User ID claim not found in request");
                    context.Result = new UnauthorizedResult();
                    return;
                }

                if (!int.TryParse(userIdClaim.Value, out int userId))
                {
                    _logger.LogError("Invalid user ID format in claim: {UserId}", userIdClaim.Value);
                    context.Result = new UnauthorizedResult();
                    return;
                }

                var hasPermission = await _permissionService.HasPermission(userId, _permission);
                if (!hasPermission)
                {
                    _logger.LogWarning(
                        "Access denied for user {UserId}. Required permission: {Permission}", 
                        userId, 
                        _permission);
                    context.Result = new ForbidResult();
                    return;
                }

                _logger.LogDebug(
                    "Access granted for user {UserId}. Permission: {Permission}", 
                    userId, 
                    _permission);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error checking permission {Permission} for request {Path}",
                    _permission,
                    context.HttpContext.Request.Path);
                context.Result = new StatusCodeResult(500);
            }
        }
    }
}
