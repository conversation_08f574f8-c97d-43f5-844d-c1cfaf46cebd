import { useState, useEffect } from "react";
import Link from "next/link";
import Navbar from '../../components/Navbar';
import styles from "../../styles/auth.module.css";
import { useAuth } from '../../contexts/AuthContext';
import { useRouter } from 'next/router';
import { toast } from 'react-toastify';
import { authService } from '../../services/apiService';

export default function Login() {
  const { login } = useAuth();
  const router = useRouter();
  const { redirect } = router.query;

  const [formData, setFormData] = useState({
    emailOrUsername: '',
    password: ''
  });

  // Kiểm tra nếu người dùng đã đăng nhập, chuyển hướng đến trang chủ hoặc trang redirect
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      if (typeof redirect === 'string' && redirect) {
        router.push(redirect);
      } else {
        router.push('/');
      }
    }
  }, [redirect, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (!formData.emailOrUsername) {
        toast.error('Vui lòng nhập email hoặc tên đăng nhập');
        return;
      }

      if (!formData.password) {
        toast.error('Vui lòng nhập mật khẩu');
        return;
      }

      // Sử dụng API service để đăng nhập
      try {
        await login(formData);
        toast.success('Đăng nhập thành công!');

        // Chuyển hướng đến trang redirect hoặc trang chủ
        if (typeof redirect === 'string' && redirect) {
          router.push(redirect);
        } else {
          router.push('/');
        }
      } catch (error: any) {
        throw error;
      }
    } catch (error: any) {
      console.error('Login error:', error);
      toast.error(error.message || 'Đăng nhập thất bại. Vui lòng thử lại!');
    }
  };

  return (
    <>
      <Navbar />
      <div className={styles.authContainer}>
        <div className={styles.authCard}>
          <h2 className={styles.authTitle}>Đăng nhập</h2>
          <form className={styles.authForm} onSubmit={handleSubmit}>
            <div className={styles.formGroup}>
              <label htmlFor="emailOrUsername">Email hoặc tên đăng nhập</label>
              <input
                type="text"
                id="emailOrUsername"
                name="emailOrUsername"
                value={formData.emailOrUsername}
                onChange={handleChange}
                placeholder="Nhập email hoặc tên đăng nhập"
                required
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="password">Mật khẩu</label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="Nhập mật khẩu"
                required
              />
            </div>
            <button type="submit" className={styles.authButton}>
              Đăng nhập
            </button>
          </form>
          <p className={styles.authSwitch}>
            Chưa có tài khoản?{" "}
            <Link href="/auth/register" className={styles.authLink}>
              Đăng ký ngay
            </Link>
          </p>
        </div>
      </div>
    </>
  );
}