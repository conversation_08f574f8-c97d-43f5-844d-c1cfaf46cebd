import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import styles from '../../styles/admin.module.css';
import { Fa<PERSON>ed, FaList, FaCalendarAlt, FaConciergebell, FaUsers, FaSignOutAlt, FaHome, FaSpa, FaConciergeBell } from 'react-icons/fa';
import { useAuth } from '../../contexts/AuthContext';

interface SidebarProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeTab, setActiveTab }) => {
  const router = useRouter();
  const { logout } = useAuth();

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  return (
    <div className={styles.sidebar}>
      <div className={styles.logo}>
        Hotel Admin
      </div>
      <nav>
        <div
          className={`${styles.navItem} ${activeTab === 'rooms' ? styles.active : ''}`}
          onClick={() => setActiveTab('rooms')}
        >
          <FaBed className={styles.navIcon} />
          <span>Quản lý phòng</span>
        </div>
        <div
          className={`${styles.navItem} ${activeTab === 'roomTypes' ? styles.active : ''}`}
          onClick={() => setActiveTab('roomTypes')}
        >
          <FaList className={styles.navIcon} />
          <span>Loại phòng</span>
        </div>
        <div
          className={`${styles.navItem} ${activeTab === 'bookings' ? styles.active : ''}`}
          onClick={() => setActiveTab('bookings')}
        >
          <FaCalendarAlt className={styles.navIcon} />
          <span>Đặt phòng</span>
        </div>
        <div
          className={`${styles.navItem} ${activeTab === 'roomServices' ? styles.active : ''}`}
          onClick={() => setActiveTab('roomServices')}
        >
          <FaSpa className={styles.navIcon} />
          <span>Dịch vụ phòng</span>
        </div>
        <div
          className={`${styles.navItem} ${activeTab === 'bookingServices' ? styles.active : ''}`}
          onClick={() => setActiveTab('bookingServices')}
        >
          <FaConciergeBell className={styles.navIcon} />
          <span>Dịch vụ đặt phòng</span>
        </div>
        <div
          className={`${styles.navItem} ${activeTab === 'users' ? styles.active : ''}`}
          onClick={() => setActiveTab('users')}
        >
          <FaUsers className={styles.navIcon} />
          <span>Người dùng</span>
        </div>
        <Link href="/" className={styles.navItem}>
          <FaHome className={styles.navIcon} />
          <span>Về trang chủ</span>
        </Link>
        <div className={styles.navItem} onClick={handleLogout}>
          <FaSignOutAlt className={styles.navIcon} />
          <span>Đăng xuất</span>
        </div>
      </nav>
    </div>
  );
};

export default Sidebar;
