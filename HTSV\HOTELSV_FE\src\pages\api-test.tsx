import React, { useState, useEffect } from 'react';
import axiosInstance from '../services/axiosInstance';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-toastify';

const ApiTest = () => {
  const { isAuthenticated } = useAuth();
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const testEndpoints = async () => {
    setLoading(true);
    setResults([]);

    const endpoints = [
      { name: 'Rooms - GetAllRoom', url: '/Rooms/GetAllRoom' },
      { name: 'RoomType - GetAll', url: '/RoomType/GetAll' },
      { name: 'RoomServices - GetAllRoomServices', url: '/RoomServices/GetAllRoomServices' },
      { name: 'User - GetAll', url: '/User/GetAll' },
      { name: 'Users - GetAll', url: '/Users/<USER>' },
      { name: 'Users - Root', url: '/Users' },
      { name: 'User - Root', url: '/User' },
      { name: 'Account - GetAll', url: '/Account/GetAll' },
      { name: 'Accounts - GetAll', url: '/Accounts/GetAll' },
      { name: 'Accounts - Root', url: '/Accounts' },
      { name: 'Account - Root', url: '/Account' },
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await axiosInstance.get(endpoint.url);
        setResults(prev => [...prev, {
          name: endpoint.name,
          url: endpoint.url,
          status: 'Success',
          statusCode: response.status,
          data: response.data
        }]);
      } catch (error: any) {
        setResults(prev => [...prev, {
          name: endpoint.name,
          url: endpoint.url,
          status: 'Error',
          statusCode: error.response?.status,
          message: error.message
        }]);
      }
    }

    setLoading(false);
  };

  useEffect(() => {
    if (isAuthenticated) {
      testEndpoints();
    }
  }, [isAuthenticated]);

  if (!isAuthenticated) {
    return (
      <div style={{ padding: '2rem', maxWidth: '800px', margin: '0 auto' }}>
        <h1>API Test</h1>
        <p>Vui lòng đăng nhập để kiểm tra API</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '2rem', maxWidth: '800px', margin: '0 auto' }}>
      <h1>API Test</h1>
      <button 
        onClick={testEndpoints}
        disabled={loading}
        style={{
          padding: '0.5rem 1rem',
          backgroundColor: '#4CAF50',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: loading ? 'not-allowed' : 'pointer',
          marginBottom: '1rem'
        }}
      >
        {loading ? 'Đang kiểm tra...' : 'Kiểm tra lại API'}
      </button>

      <div>
        <h2>Kết quả:</h2>
        {results.map((result, index) => (
          <div 
            key={index}
            style={{
              border: '1px solid #ddd',
              borderRadius: '4px',
              padding: '1rem',
              marginBottom: '1rem',
              backgroundColor: result.status === 'Success' ? '#f0fff0' : '#fff0f0'
            }}
          >
            <h3>{result.name} ({result.url})</h3>
            <p><strong>Trạng thái:</strong> {result.status} {result.statusCode && `(${result.statusCode})`}</p>
            {result.status === 'Success' ? (
              <div>
                <p><strong>Dữ liệu:</strong></p>
                <pre style={{ 
                  backgroundColor: '#f5f5f5', 
                  padding: '0.5rem', 
                  borderRadius: '4px',
                  overflow: 'auto',
                  maxHeight: '200px'
                }}>
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              </div>
            ) : (
              <p><strong>Lỗi:</strong> {result.message}</p>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ApiTest;
