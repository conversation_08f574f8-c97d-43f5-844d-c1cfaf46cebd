using Microsoft.AspNetCore.Mvc;
using HOTELSV_BE.Models;
using System.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Dapper;
using System.Data;
using HOTELSV_BE.Services;

namespace HOTELSV_BE.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly string _connectionString;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthController> _logger;
        private readonly IPermissionService _permissionService;

        public AuthController(
            ILogger<AuthController> logger, 
            IConfiguration configuration,
            IPermissionService permissionService)
        {
            _logger = logger;
            _connectionString = configuration.GetConnectionString("DefaultConnection");
            _configuration = configuration;
            _permissionService = permissionService;
        }

        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] RegisterRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                using (var connection = new SqlConnection(_connectionString))
                {
                    // Kiểm tra email và username đã tồn tại
                    var existingUser = await connection.QueryFirstOrDefaultAsync<int>(
                        "SELECT COUNT(*) FROM Users WHERE Email = @Email OR UserName = @UserName",
                        new { request.Email, request.Username }
                    );

                    if (existingUser > 0)
                    {
                        return BadRequest(new { message = "Email hoặc username đã tồn tại" });
                    }

                    var parameters = new DynamicParameters();
                    parameters.Add("@UserName", request.Username);
                    parameters.Add("@Email", request.Email);
                    parameters.Add("@Password", request.Password);
                    parameters.Add("@Phone", request.Phone ?? "");
                    parameters.Add("@FirstName", request.FirstName ?? "");
                    parameters.Add("@LastName", request.LastName ?? "");

                    var result = await connection.ExecuteAsync(
                "Users_CreateUser",
                new
                {
                    request.Username,
                    request.Email,
                    Password = request.Password,
                    Phone = request.Phone ?? "",
                    FirstName = request.FirstName ?? "",
                    LastName = request.LastName ?? ""
                },
                commandType: CommandType.StoredProcedure
            );
                    if (result == 0)
                    {
                        return BadRequest(new
                        {
                            success = false,
                            message = "Đăng ký không thành công"
                        });
                    }

                    return Ok(new
                    {
                        success = true,
                        message = "Đăng ký thành công"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đăng ký");
                return StatusCode(500, new { message = "Lỗi server khi đăng ký" });
            }
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Email) && string.IsNullOrEmpty(request.Username))
                {
                    return BadRequest(new { message = "Email hoặc username là bắt buộc" });
                }

                if (string.IsNullOrEmpty(request.Password))
                {
                    return BadRequest(new { message = "Password là bắt buộc" });
                }

                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@EmailOrUserName", 
                        string.IsNullOrEmpty(request.Email) ? request.Username : request.Email);
                    parameters.Add("@Password", request.Password);

                    var user = await connection.QueryFirstOrDefaultAsync<dynamic>(
                        "Users_CheckLogin",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (user == null)
                    {
                        return Unauthorized(new { message = "Email/Username hoặc mật khẩu không chính xác" });
                    }

                    // Lấy permissions của user
                    var userPermissions = await _permissionService.GetUserPermissions(user.UserId);

                    // Tạo JWT token
                    var tokenHandler = new JwtSecurityTokenHandler();
                    var key = _configuration["Jwt:Key"];
                    if (string.IsNullOrEmpty(key))
                    {
                        _logger.LogError("Thiếu cấu hình JWT Key");
                        return StatusCode(500, new { message = "Lỗi server: Thiếu cấu hình JWT" });
                    }

                    var keyBytes = Encoding.UTF8.GetBytes(key);
                    var claims = new List<Claim>
                    {
                        new Claim(ClaimTypes.NameIdentifier, user.UserId.ToString()),
                        new Claim(ClaimTypes.Name, user.UserName ?? string.Empty)
                    };

                    // Thêm permissions vào claims
                    foreach (var permission in userPermissions.Permissions)
                    {
                        claims.Add(new Claim("permission", permission));
                    }

                    // Thêm roles vào claims
                    foreach (var role in userPermissions.Roles)
                    {
                        claims.Add(new Claim(ClaimTypes.Role, role));
                    }

                    if (!string.IsNullOrEmpty(user.Email))
                    {
                        claims.Add(new Claim(ClaimTypes.Email, user.Email));
                    }

                    var tokenDescriptor = new SecurityTokenDescriptor
                    {
                        Subject = new ClaimsIdentity(claims),
                        Expires = DateTime.UtcNow.AddHours(1),
                        Issuer = _configuration["Jwt:Issuer"],
                        Audience = _configuration["Jwt:Issuer"],
                        SigningCredentials = new SigningCredentials(
                            new SymmetricSecurityKey(keyBytes),
                            SecurityAlgorithms.HmacSha256Signature)
                    };

                    var token = tokenHandler.CreateToken(tokenDescriptor);

                    return Ok(new
                    {
                        success = true,
                        message = "Đăng nhập thành công",
                        data = new AuthResponse
                        {
                            Token = tokenHandler.WriteToken(token),
                            Expiration = tokenDescriptor.Expires.Value,
                            UserId = user.UserId,
                            Username = user.UserName,
                            Email = user.Email,
                            Roles = userPermissions.Roles,
                            Permissions = userPermissions.Permissions
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi đăng nhập");
                return StatusCode(500, new { message = "Lỗi server khi đăng nhập" });
            }
        }

        [HttpPost("logout")]
        public IActionResult Logout()
        {
            return Ok(new { message = "Đăng xuất thành công" });
        }
    }
}