﻿USE [data_QLKS_113_Nhom1]
GO
/****** Object:  Table [dbo].[Permissions]    Script Date: 5/21/2025 11:03:50 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Permissions](
	[PermissionId] [int] IDENTITY(1,1) NOT NULL,
	[PermissionName] [nvarchar](100) NOT NULL,
	[Description] [nvarchar](200) NULL,
PRIMARY KEY CLUSTERED 
(
	[PermissionId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[RolePermissions]    Script Date: 5/21/2025 11:03:50 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[RolePermissions](
	[RolePermissionId] [int] IDENTITY(1,1) NOT NULL,
	[RoleId] [int] NULL,
	[PermissionId] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[RolePermissionId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Roles]    Script Date: 5/21/2025 11:03:50 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Roles](
	[RoleId] [int] IDENTITY(1,1) NOT NULL,
	[RoleName] [nvarchar](50) NOT NULL,
	[Description] [nvarchar](200) NULL,
PRIMARY KEY CLUSTERED 
(
	[RoleId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[UserRoles]    Script Date: 5/21/2025 11:03:50 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[UserRoles](
	[UserRoleId] [int] IDENTITY(1,1) NOT NULL,
	[UserId] [int] NULL,
	[RoleId] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[UserRoleId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Users]    Script Date: 5/21/2025 11:03:50 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Users](
	[UserId] [int] IDENTITY(1,1) NOT NULL,
	[Username] [nvarchar](50) NOT NULL,
	[PasswordHash] [nvarchar](200) NOT NULL,
	[Email] [nvarchar](100) NULL,
	[FirstName] [nvarchar](50) NULL,
	[LastName] [nvarchar](50) NULL,
	[Phone] [nvarchar](20) NULL,
	[IsActive] [bit] NULL,
	[CreatedDate] [datetime] NULL,
	[LastLogin] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
	[UserId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
SET IDENTITY_INSERT [dbo].[Permissions] ON 
GO
INSERT [dbo].[Permissions] ([PermissionId], [PermissionName], [Description]) VALUES (1, N'view_bookings', N'Xem thông tin d?t phòng')
GO
INSERT [dbo].[Permissions] ([PermissionId], [PermissionName], [Description]) VALUES (2, N'create_booking', N'T?o don d?t phòng m?i')
GO
INSERT [dbo].[Permissions] ([PermissionId], [PermissionName], [Description]) VALUES (3, N'edit_booking', N'Ch?nh s?a don d?t phòng')
GO
INSERT [dbo].[Permissions] ([PermissionId], [PermissionName], [Description]) VALUES (4, N'cancel_booking', N'H?y don d?t phòng')
GO
INSERT [dbo].[Permissions] ([PermissionId], [PermissionName], [Description]) VALUES (5, N'view_rooms', N'Xem thông tin phòng')
GO
INSERT [dbo].[Permissions] ([PermissionId], [PermissionName], [Description]) VALUES (6, N'assign_rooms', N'Phân phòng cho khách')
GO
INSERT [dbo].[Permissions] ([PermissionId], [PermissionName], [Description]) VALUES (7, N'view_customers', N'Xem thông tin khách hàng')
GO
INSERT [dbo].[Permissions] ([PermissionId], [PermissionName], [Description]) VALUES (8, N'edit_customers', N'Ch?nh s?a thông tin khách hàng')
GO
INSERT [dbo].[Permissions] ([PermissionId], [PermissionName], [Description]) VALUES (9, N'process_payments', N'X? lý thanh toán')
GO
INSERT [dbo].[Permissions] ([PermissionId], [PermissionName], [Description]) VALUES (10, N'create_invoice', N'T?o hóa don')
GO
INSERT [dbo].[Permissions] ([PermissionId], [PermissionName], [Description]) VALUES (11, N'edit_invoice', N'Ch?nh s?a hóa don')
GO
INSERT [dbo].[Permissions] ([PermissionId], [PermissionName], [Description]) VALUES (12, N'view_invoices', N'Xem hóa don')
GO
INSERT [dbo].[Permissions] ([PermissionId], [PermissionName], [Description]) VALUES (13, N'view_employees', N'Xem thông tin nhân viên')
GO
INSERT [dbo].[Permissions] ([PermissionId], [PermissionName], [Description]) VALUES (14, N'edit_employees', N'Ch?nh s?a thông tin nhân viên')
GO
INSERT [dbo].[Permissions] ([PermissionId], [PermissionName], [Description]) VALUES (15, N'view_reports', N'Xem báo cáo')
GO
SET IDENTITY_INSERT [dbo].[Permissions] OFF
GO
SET IDENTITY_INSERT [dbo].[RolePermissions] ON 
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (1, 1, 1)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (2, 1, 2)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (3, 1, 3)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (4, 1, 4)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (5, 1, 5)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (6, 1, 6)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (7, 1, 7)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (8, 1, 8)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (9, 1, 9)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (10, 1, 10)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (11, 1, 11)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (12, 1, 12)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (13, 1, 13)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (14, 1, 14)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (15, 1, 15)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (16, 2, 1)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (17, 2, 2)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (18, 2, 3)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (19, 2, 4)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (20, 2, 5)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (21, 2, 6)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (22, 2, 7)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (23, 2, 8)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (24, 2, 9)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (25, 2, 10)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (26, 2, 11)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (27, 2, 12)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (28, 2, 13)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (29, 2, 14)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (30, 2, 15)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (31, 3, 1)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (32, 3, 2)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (33, 3, 3)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (34, 3, 4)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (35, 3, 5)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (36, 3, 6)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (37, 3, 7)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (38, 3, 8)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (39, 3, 9)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (40, 3, 10)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (41, 3, 12)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (42, 4, 5)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (43, 5, 5)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (44, 6, 1)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (45, 6, 2)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (46, 6, 4)
GO
INSERT [dbo].[RolePermissions] ([RolePermissionId], [RoleId], [PermissionId]) VALUES (47, 6, 12)
GO
SET IDENTITY_INSERT [dbo].[RolePermissions] OFF
GO
SET IDENTITY_INSERT [dbo].[Roles] ON 
GO
INSERT [dbo].[Roles] ([RoleId], [RoleName], [Description]) VALUES (1, N'Administrator', N'Qu?n tr? h? th?ng v?i quy?n truy c?p d?y d?')
GO
INSERT [dbo].[Roles] ([RoleId], [RoleName], [Description]) VALUES (2, N'Manager', N'Qu?n lý khách s?n')
GO
INSERT [dbo].[Roles] ([RoleId], [RoleName], [Description]) VALUES (3, N'Receptionist', N'Nhân viên l? tân')
GO
INSERT [dbo].[Roles] ([RoleId], [RoleName], [Description]) VALUES (4, N'Housekeeper', N'Nhân viên d?n phòng')
GO
INSERT [dbo].[Roles] ([RoleId], [RoleName], [Description]) VALUES (5, N'Maintenance', N'Nhân viên b?o trì')
GO
INSERT [dbo].[Roles] ([RoleId], [RoleName], [Description]) VALUES (6, N'Customer', N'Khách hàng')
GO
SET IDENTITY_INSERT [dbo].[Roles] OFF
GO
SET IDENTITY_INSERT [dbo].[UserRoles] ON 
GO
INSERT [dbo].[UserRoles] ([UserRoleId], [UserId], [RoleId]) VALUES (1, 1, 1)
GO
INSERT [dbo].[UserRoles] ([UserRoleId], [UserId], [RoleId]) VALUES (2, 2, 6)
GO
INSERT [dbo].[UserRoles] ([UserRoleId], [UserId], [RoleId]) VALUES (3, 3, 2)
GO
INSERT [dbo].[UserRoles] ([UserRoleId], [UserId], [RoleId]) VALUES (4, 4, 3)
GO
INSERT [dbo].[UserRoles] ([UserRoleId], [UserId], [RoleId]) VALUES (5, 5, 4)
GO
INSERT [dbo].[UserRoles] ([UserRoleId], [UserId], [RoleId]) VALUES (6, 6, 5)
GO
INSERT [dbo].[UserRoles] ([UserRoleId], [UserId], [RoleId]) VALUES (7, 7, 6)
GO
INSERT [dbo].[UserRoles] ([UserRoleId], [UserId], [RoleId]) VALUES (8, 8, 6)
GO
INSERT [dbo].[UserRoles] ([UserRoleId], [UserId], [RoleId]) VALUES (9, 9, 6)
GO
INSERT [dbo].[UserRoles] ([UserRoleId], [UserId], [RoleId]) VALUES (10, 10, 6)
GO
INSERT [dbo].[UserRoles] ([UserRoleId], [UserId], [RoleId]) VALUES (11, 11, 6)
GO
INSERT [dbo].[UserRoles] ([UserRoleId], [UserId], [RoleId]) VALUES (12, 12, 6)
GO
SET IDENTITY_INSERT [dbo].[UserRoles] OFF
GO
SET IDENTITY_INSERT [dbo].[Users] ON 
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (1, N'admin', N'hashed_password_here', N'<EMAIL>', N'Nguyễn', N'Văn A', N'0901234567', 1, CAST(N'2025-03-31T23:06:52.203' AS DateTime), NULL)
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (2, N'user1', N'E3B0C44298FC1C149AFBF4C8996FB92427AE41E4649B934CA495991B7852B855', N'<EMAIL>', N'User', N'One', N'0987654321', 1, CAST(N'2025-03-31T23:06:52.207' AS DateTime), NULL)
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (3, N'manager1', N'hashed_password', N'<EMAIL>', N'Lê', N'Thị C', N'0903456789', 1, CAST(N'2025-03-31T23:12:03.830' AS DateTime), NULL)
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (4, N'staff1', N'hashed_password', N'<EMAIL>', N'Phạm', N'Văn D', N'0904567890', 1, CAST(N'2025-03-31T23:12:03.830' AS DateTime), NULL)
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (5, N'staff2', N'hashed_password', N'<EMAIL>', N'Hoàng', N'Thị E', N'0905678901', 1, CAST(N'2025-03-31T23:12:03.830' AS DateTime), NULL)
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (6, N'staff3', N'hashed_password', N'<EMAIL>', N'Trần', N'Văn F', N'0906789012', 1, CAST(N'2025-03-31T23:12:03.830' AS DateTime), NULL)
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (7, N'customer2', N'hashed_password', N'<EMAIL>', N'Nguyễn', N'Thị G', N'0907890123', 1, CAST(N'2025-03-31T23:12:03.830' AS DateTime), NULL)
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (8, N'customer3', N'hashed_password', N'<EMAIL>', N'Lý', N'Văn H', N'0908901234', 1, CAST(N'2025-03-31T23:12:03.830' AS DateTime), NULL)
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (9, N'customer4', N'hashed_password', N'<EMAIL>', N'Phan', N'Thị I', N'0909012345', 1, CAST(N'2025-03-31T23:12:03.830' AS DateTime), NULL)
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (10, N'customer5', N'hashed_password', N'<EMAIL>', N'Vũ', N'Văn J', N'0910123456', 1, CAST(N'2025-03-31T23:12:03.830' AS DateTime), NULL)
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (11, N'customer6', N'hashed_password', N'<EMAIL>', N'Đỗ', N'Thị K', N'0911234567', 1, CAST(N'2025-03-31T23:12:03.830' AS DateTime), NULL)
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (12, N'customer7', N'hashed_password', N'<EMAIL>', N'Mai', N'Văn L', N'0912345678', 1, CAST(N'2025-03-31T23:12:03.830' AS DateTime), NULL)
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (54, N'nauhT1', N'4459F0E08BAA873FDB99935305E861662DF3789DF3E31039217EE7CE34DB84BA', N'<EMAIL>', N'Trần', N'Trọng Thuận', N'0912345678', 1, CAST(N'2025-04-20T01:30:03.053' AS DateTime), CAST(N'2025-04-20T23:59:17.537' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (55, N'string', N'288A771EBF8EF6A3C7B1E2ECDD87DAC9CFEF02BE94724EEEC526D381DE11396D', N'<EMAIL>', N'string', N'string', N'string', 1, CAST(N'2025-04-22T15:23:54.207' AS DateTime), CAST(N'2025-04-23T15:03:49.503' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (56, N'tuan98', N'AFFF7E8B1FED5251BC8DC878012A3BE715EC1FCD20C796AC8F3ECBEAAC44654C', N'<EMAIL>', NULL, NULL, NULL, 1, CAST(N'2025-04-23T14:35:34.003' AS DateTime), NULL)
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (57, N'minh', N'123456', N'<EMAIL>', N'minh', N'nguyen', N'**********', 1, CAST(N'2025-04-29T14:56:22.907' AS DateTime), CAST(N'2025-04-29T14:57:18.463' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (58, N'Nguyen Van Minh', N'EC278A38901287B2771A13739520384D43E4B078F78AFFE702DEF108774CCE24', N'<EMAIL>', N'minh', N'minh', N'**********', 1, CAST(N'2025-04-30T20:47:54.437' AS DateTime), NULL)
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (59, N'minh02', N'EC278A38901287B2771A13739520384D43E4B078F78AFFE702DEF108774CCE24', N'<EMAIL>', N'minh', N'minh', N'**********', 1, CAST(N'2025-04-30T20:52:31.547' AS DateTime), NULL)
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (60, N'minh03', N'24E242F9A2AED244A577CBAA6E0FB966B476CCE38C71B75BDAFFFCB64DCA2E41', N'<EMAIL>', N'minh', N'minh', N'**********', 1, CAST(N'2025-04-30T21:00:38.910' AS DateTime), CAST(N'2025-04-30T21:35:36.703' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (61, N'minh05', N'24E242F9A2AED244A577CBAA6E0FB966B476CCE38C71B75BDAFFFCB64DCA2E41', N'<EMAIL>', N'minh', N'minh', N'**********', 1, CAST(N'2025-04-30T21:36:15.863' AS DateTime), CAST(N'2025-04-30T21:36:28.260' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (62, N'minh06', N'24E242F9A2AED244A577CBAA6E0FB966B476CCE38C71B75BDAFFFCB64DCA2E41', N'<EMAIL>', N'minh', N'minh', N'**********', 1, CAST(N'2025-04-30T21:40:12.750' AS DateTime), CAST(N'2025-04-30T21:40:24.787' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (63, N'minh07', N'24E242F9A2AED244A577CBAA6E0FB966B476CCE38C71B75BDAFFFCB64DCA2E41', N'<EMAIL>', N'minh', N'minh', N'**********', 1, CAST(N'2025-04-30T21:41:09.523' AS DateTime), CAST(N'2025-05-03T09:22:56.583' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (64, N'NguyenVanMinh', N'24E242F9A2AED244A577CBAA6E0FB966B476CCE38C71B75BDAFFFCB64DCA2E41', N'<EMAIL>', N'Minh ', N'Nguyen', N'**********', 1, CAST(N'2025-04-30T21:53:44.757' AS DateTime), CAST(N'2025-04-30T21:53:57.180' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (65, N'minh2', N'24E242F9A2AED244A577CBAA6E0FB966B476CCE38C71B75BDAFFFCB64DCA2E41', N'<EMAIL>', N'minh', N'minh', N'1234567892', 1, CAST(N'2025-04-30T22:53:20.470' AS DateTime), CAST(N'2025-04-30T22:53:31.680' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (66, N'asdasdasdasdasdsadsadasd', N'A8A94F5EC53F70618CEE3C680562355EE4847F581DF238D710C846D0F198DFD6', N'<EMAIL>', N'string', N'string', N'0123456987', 1, CAST(N'2025-05-02T15:11:01.303' AS DateTime), CAST(N'2025-05-02T15:13:00.783' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (67, N'string677', N'string', N'<EMAIL>', N'string', N'string', N'string', 1, CAST(N'2025-05-02T15:20:08.757' AS DateTime), CAST(N'2025-05-02T16:08:57.373' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (68, N'string79', N'A8A94F5EC53F70618CEE3C680562355EE4847F581DF238D710C846D0F198DFD6', N'<EMAIL>', N'string', N'string', N'string', 1, CAST(N'2025-05-02T15:24:17.440' AS DateTime), CAST(N'2025-05-02T16:20:52.523' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (69, N'string15', N'6AD3C2788B2AC2F5BD2A9A241B2F74BF5BA70294B0FEB788FBB66E758F0B8C1F', N'<EMAIL>', N'string', N'string', N'string', 1, CAST(N'2025-05-02T16:20:10.237' AS DateTime), CAST(N'2025-05-02T16:20:43.333' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (70, N'string969', N'288A771EBF8EF6A3C7B1E2ECDD87DAC9CFEF02BE94724EEEC526D381DE11396D', N'<EMAIL>', N'string', N'string', N'4234234234234', 1, CAST(N'2025-05-02T16:35:10.570' AS DateTime), CAST(N'2025-05-02T16:52:08.870' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (71, N'minh08', N'EC278A38901287B2771A13739520384D43E4B078F78AFFE702DEF108774CCE24', N'<EMAIL>', N'Minh', N'Nguyen', N'**********', 1, CAST(N'2025-05-03T07:59:17.430' AS DateTime), CAST(N'2025-05-20T15:52:42.053' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (72, N'minh09', N'24E242F9A2AED244A577CBAA6E0FB966B476CCE38C71B75BDAFFFCB64DCA2E41', N'<EMAIL>', N'minh', N'dê', N'123845731', 1, CAST(N'2025-05-03T08:21:06.030' AS DateTime), CAST(N'2025-05-03T08:28:47.793' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (73, N'Minh8', N'24E242F9A2AED244A577CBAA6E0FB966B476CCE38C71B75BDAFFFCB64DCA2E41', N'<EMAIL>', N'Minh', N'Minh', N'**********', 1, CAST(N'2025-05-03T12:47:28.723' AS DateTime), CAST(N'2025-05-03T12:47:34.230' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (74, N'khang', N'FB2E52C628A01046BCC548C4242C9FA59BCFCE9B5865F33A0CC70ADCA16C20F5', N'<EMAIL>', N'string', N'string', N'string', 1, CAST(N'2025-05-07T13:56:29.283' AS DateTime), CAST(N'2025-05-07T15:03:07.833' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (75, N'RealAccount', N'FB2E52C628A01046BCC548C4242C9FA59BCFCE9B5865F33A0CC70ADCA16C20F5', N'<EMAIL>', N'Real', N'Account', N'**********', 1, CAST(N'2025-05-07T20:49:15.870' AS DateTime), CAST(N'2025-05-07T20:49:33.370' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (76, N'sasa', N'FB2E52C628A01046BCC548C4242C9FA59BCFCE9B5865F33A0CC70ADCA16C20F5', N'<EMAIL>', N'khang', N'Trần', N'**********', 1, CAST(N'2025-05-13T13:53:40.617' AS DateTime), CAST(N'2025-05-13T13:54:15.773' AS DateTime))
GO
INSERT [dbo].[Users] ([UserId], [Username], [PasswordHash], [Email], [FirstName], [LastName], [Phone], [IsActive], [CreatedDate], [LastLogin]) VALUES (77, N'thuan', N'288A771EBF8EF6A3C7B1E2ECDD87DAC9CFEF02BE94724EEEC526D381DE11396D', N'<EMAIL>', N'string', N'string', N'string', 1, CAST(N'2025-05-20T15:24:03.783' AS DateTime), CAST(N'2025-05-20T18:28:49.157' AS DateTime))
GO
SET IDENTITY_INSERT [dbo].[Users] OFF
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [UQ_Users_Username]    Script Date: 5/21/2025 11:03:50 AM ******/
ALTER TABLE [dbo].[Users] ADD  CONSTRAINT [UQ_Users_Username] UNIQUE NONCLUSTERED 
(
	[Username] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
ALTER TABLE [dbo].[Users] ADD  DEFAULT ((1)) FOR [IsActive]
GO
ALTER TABLE [dbo].[Users] ADD  DEFAULT (getdate()) FOR [CreatedDate]
GO
ALTER TABLE [dbo].[RolePermissions]  WITH CHECK ADD FOREIGN KEY([PermissionId])
REFERENCES [dbo].[Permissions] ([PermissionId])
GO
ALTER TABLE [dbo].[RolePermissions]  WITH CHECK ADD FOREIGN KEY([RoleId])
REFERENCES [dbo].[Roles] ([RoleId])
GO
ALTER TABLE [dbo].[UserRoles]  WITH CHECK ADD FOREIGN KEY([RoleId])
REFERENCES [dbo].[Roles] ([RoleId])
GO
ALTER TABLE [dbo].[UserRoles]  WITH CHECK ADD FOREIGN KEY([UserId])
REFERENCES [dbo].[Users] ([UserId])
GO
