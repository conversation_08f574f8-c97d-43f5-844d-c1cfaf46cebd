import { employeeApi } from './api';

const employeeService = {
  async getAllEmployees() {
    try {
      const response = await employeeApi.getAllEmployees();
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách nhân viên');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getEmployeeById(id: number) {
    try {
      const response = await employeeApi.getEmployeeById(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải thông tin nhân viên');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async addEmployee(employeeData: any) {
    try {
      const response = await employeeApi.addEmployee(employeeData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || '<PERSON>hông thể thêm nhân viên');
      }
      throw new Error('<PERSON>hông thể kết nối đến server');
    }
  },

  async updateEmployee(id: number, employeeData: any) {
    try {
      const response = await employeeApi.updateEmployee(id, employeeData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể cập nhật nhân viên');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async deleteEmployee(id: number) {
    try {
      const response = await employeeApi.deleteEmployee(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể xóa nhân viên');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getEmployeesPaginated(filter: any) {
    try {
      const response = await employeeApi.getEmployeesPaginated(filter);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách nhân viên');
      }
      throw new Error('Không thể kết nối đến server');
    }
  }
};

export { employeeService };