import { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Chỉ cho phép phương thức POST
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method Not Allowed' });
  }

  try {
    // Lấy URL và dữ liệu từ request
    const { url, data, token } = req.body;

    if (!url) {
      return res.status(400).json({ success: false, message: 'URL is required' });
    }

    // Chuẩn bị headers
    const headers: any = {
      'Content-Type': 'application/json',
    };

    // Thêm token nếu có
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    console.log('Proxy request to:', url);
    console.log('Proxy data:', data);
    console.log('Proxy headers:', headers);

    // <PERSON><PERSON><PERSON> request đến API thực tế
    const response = await axios.post(url, data, { headers });

    // Tr<PERSON> về kết quả
    return res.status(response.status).json(response.data);
  } catch (error: any) {
    console.error('Proxy error:', error);

    // Trả về lỗi
    return res.status(error.response?.status || 500).json({
      success: false,
      message: error.response?.data?.message || error.message || 'Internal Server Error',
      error: error.response?.data || error.toString()
    });
  }
}
