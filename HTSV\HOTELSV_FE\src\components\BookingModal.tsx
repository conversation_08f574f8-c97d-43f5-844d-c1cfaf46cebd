import React, { useState } from 'react';
import styles from '../styles/bookingModal.module.css';
import { bookingService } from '../services/apiService';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-toastify';
import { useRouter } from 'next/router';

interface BookingModalProps {
  isOpen: boolean;
  onClose: () => void;
  room: {
    name: string;
    price: string;
    image: string;
    roomId?: number;
    roomTypeId: number;
  };
}

const BookingModal: React.FC<BookingModalProps> = ({ isOpen, onClose, room }) => {
  if (!isOpen) return null;

  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  // Không cần kiểm tra đăng nhập ở đây nữa vì đã xử lý trong RoomList
  // Chỉ hiển thị modal khi người dùng đã đăng nhập (isAuthenticated = true)
  if (!isAuthenticated) return null;

  const [bookingData, setBookingData] = useState({
    fullName: user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : '',
    phoneNumber: user?.phone || '',
    checkIn: '',
    checkOut: '',
    guests: 1,
    specialRequests: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setBookingData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Kiểm tra các trường bắt buộc
    if (!bookingData.fullName || !bookingData.phoneNumber || !bookingData.checkIn || !bookingData.checkOut) {
      toast.error('Vui lòng điền đầy đủ thông tin bắt buộc');
      return;
    }

    // Kiểm tra ngày nhận và trả phòng
    const checkInDate = new Date(bookingData.checkIn);
    const checkOutDate = new Date(bookingData.checkOut);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (checkInDate < today) {
      toast.error('Ngày nhận phòng không thể là ngày trong quá khứ');
      return;
    }

    if (checkOutDate <= checkInDate) {
      toast.error('Ngày trả phòng phải sau ngày nhận phòng');
      return;
    }

    try {
      setLoading(true);

      // Chuẩn bị thông tin ghi chú
      const guestInfo = `Họ tên: ${bookingData.fullName}, SĐT: ${bookingData.phoneNumber}`;
      const specialRequests = bookingData.specialRequests ? `, Yêu cầu đặc biệt: ${bookingData.specialRequests}` : '';
      const notesContent = guestInfo + specialRequests;

      // Định dạng ngày tháng theo chuẩn yyyy-MM-dd để phù hợp với API
      const formatDate = (dateString: string) => {
        try {
          // Đảm bảo rằng dateString là một chuỗi hợp lệ
          if (!dateString) {
            throw new Error('Ngày không hợp lệ');
          }

          // Tạo đối tượng Date từ chuỗi ngày
          const date = new Date(dateString);

          // Kiểm tra xem date có hợp lệ không
          if (isNaN(date.getTime())) {
            throw new Error('Ngày không hợp lệ');
          }

          // Đặt giờ là 12:00 để tránh vấn đề múi giờ
          date.setHours(12, 0, 0, 0);

          // Format ngày theo định dạng yyyy-MM-dd
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');

          return `${year}-${month}-${day}`;
        } catch (error) {
          console.error('Lỗi khi định dạng ngày:', error);
          toast.error('Ngày không hợp lệ. Vui lòng kiểm tra lại.');
          throw error;
        }
      };

      // Kiểm tra user và userId
      if (!user) {
        toast.info('Vui lòng đăng nhập để đặt phòng');
        router.push('/auth/login');
        return;
      }

      // Lấy userId từ user object và chuyển đổi sang số nếu cần
      // Lưu ý: userId có thể là user.userId, user.id, hoặc user.UserId
      let customerId;

      // Log toàn bộ thông tin user để debug
      console.log('User info:', user);

      // Kiểm tra tất cả các trường có thể chứa userId
      if (user.userId) {
        customerId = typeof user.userId === 'string' ? parseInt(user.userId) : user.userId;
      } else if (user.id) {
        customerId = typeof user.id === 'string' ? parseInt(user.id) : user.id;
      } else if (user.UserId) {
        customerId = typeof user.UserId === 'string' ? parseInt(user.UserId) : user.UserId;
      } else {
        // Nếu không tìm thấy userId trong các trường thông thường, tìm kiếm trong toàn bộ object
        for (const key in user) {
          if (key.toLowerCase().includes('userid') || key.toLowerCase().includes('id')) {
            customerId = user[key];
            console.log(`Tìm thấy ID trong trường ${key}:`, customerId);
            break;
          }
        }
      }

      // Nếu vẫn không tìm thấy, sử dụng giá trị mặc định (chỉ cho mục đích test)
      if (!customerId) {
        console.warn('Không tìm thấy customerId trong thông tin user, sử dụng ID mặc định');
        customerId = 1; // ID mặc định cho mục đích test
      }

      console.log('Customer ID được sử dụng:', customerId);

      // Tạo đối tượng booking request theo đúng cấu trúc API
      // Đảm bảo đúng với cấu trúc BookingRequest trong backend
      const bookingRequest = {
        customerId: customerId, // ID của khách hàng đã đăng nhập và kiểm tra
        employeeId: 1, // Mặc định là 1, có thể thay đổi tùy theo yêu cầu
        checkInDate: formatDate(bookingData.checkIn), // Ngày nhận phòng từ form, định dạng ISO
        checkOutDate: formatDate(bookingData.checkOut), // Ngày trả phòng từ form, định dạng ISO
        roomTypeId: room.roomTypeId, // ID loại phòng từ thông tin phòng
        quantity: bookingData.guests, // Số lượng khách từ form
        notes: notesContent, // Thông tin khách hàng và yêu cầu đặc biệt
        bookingSource: 'Website' // Nguồn đặt phòng
      };

      console.log('Booking request:', bookingRequest); // Log để kiểm tra

      try {
        // Kiểm tra lại dữ liệu trước khi gửi
        if (!bookingRequest.roomTypeId) {
          toast.error('Không tìm thấy thông tin loại phòng. Vui lòng thử lại.');
          return;
        }

        // Kiểm tra các trường bắt buộc khác
        if (!bookingRequest.checkInDate || !bookingRequest.checkOutDate) {
          toast.error('Vui lòng chọn ngày nhận phòng và trả phòng.');
          return;
        }

        // Log request để debug
        console.log('Final booking request:', JSON.stringify(bookingRequest, null, 2));

        // Sử dụng API service để đặt phòng
        console.log('Gửi request đặt phòng:', bookingRequest);

        try {
          console.log('Gửi request đặt phòng với dữ liệu:', JSON.stringify(bookingRequest, null, 2));

          // Kiểm tra lại định dạng ngày tháng
          console.log('Check-in date:', bookingRequest.checkInDate);
          console.log('Check-out date:', bookingRequest.checkOutDate);

          // Gọi API đặt phòng
          const response = await bookingService.addBooking(bookingRequest);
          console.log('Kết quả đặt phòng:', response);

          if (response && response.success) {
            toast.success('Đặt phòng thành công!');
            router.push('/booking'); // Chuyển đến trang booking
          } else {
            console.error('API response không thành công:', response);
            throw new Error(response?.message || 'Đặt phòng thất bại. Vui lòng thử lại!');
          }
        } catch (error: any) {
          console.error('Lỗi khi gọi API đặt phòng:', error);

          // Xử lý lỗi từ fetch API
          if (error.message && error.message.includes('HTTP error! Status: 405')) {
            console.error('Lỗi 405 Method Not Allowed');
            toast.error('Phương thức không được phép. Vui lòng liên hệ quản trị viên.');
          } else if (error.message && error.message.includes('HTTP error! Status: 400')) {
            console.error('Lỗi 400 Bad Request');
            toast.error('Dữ liệu đặt phòng không hợp lệ. Vui lòng kiểm tra lại thông tin.');
          } else if (error.message && error.message.includes('HTTP error!')) {
            console.error('HTTP error:', error.message);
            toast.error(`Lỗi kết nối: ${error.message}`);
          } else {
            // Lỗi khác
            toast.error(error.message || 'Đặt phòng thất bại. Vui lòng thử lại!');
          }

          throw error; // Ném lỗi để xử lý ở catch bên ngoài
        }
      } catch (apiError: any) {
        console.error('API Error booking room:', apiError);

        // Xử lý các loại lỗi phổ biến
        if (apiError.message && apiError.message.includes('FOREIGN KEY constraint')) {
          console.error('Foreign key constraint error:', apiError);
          toast.error('Không thể đặt phòng: Thông tin khách hàng không hợp lệ. Vui lòng liên hệ quản trị viên.');
        } else if (apiError.message && apiError.message.includes('Không đủ phòng trống')) {
          toast.error('Không đủ phòng trống cho thời gian bạn chọn. Vui lòng chọn ngày khác hoặc loại phòng khác.');
        } else if (apiError.response && apiError.response.data) {
          // Xử lý lỗi từ API response
          console.error('API response error:', apiError.response.data);
          toast.error(apiError.response.data.message || 'Đặt phòng thất bại. Vui lòng thử lại!');
        } else {
          // Lỗi khác
          toast.error(apiError.message || 'Đặt phòng thất bại. Vui lòng thử lại!');
        }
      }
    } catch (error: any) {
      console.error('General error booking room:', error);
      toast.error('Có lỗi xảy ra khi đặt phòng. Vui lòng thử lại sau!');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.modalOverlay} onClick={onClose}>
      <div className={styles.modal} onClick={e => e.stopPropagation()}>
        <button className={styles.closeButton} onClick={onClose}>&times;</button>

        <div className={styles.modalContent}>
          <div className={styles.roomInfo}>
            <img src={room.image} alt={room.name} className={styles.roomImage} />
            <div className={styles.roomDetails}>
              <h3>{room.name}</h3>
              <p className={styles.price}>{room.price}</p>
            </div>
          </div>

          <form onSubmit={handleSubmit} className={styles.bookingForm}>
            <div className={styles.formGroup}>
              <label>Họ và tên</label>
              <input
                type="text"
                name="fullName"
                value={bookingData.fullName}
                onChange={handleChange}
                placeholder="Nhập họ và tên"
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label>Số điện thoại</label>
              <input
                type="tel"
                name="phoneNumber"
                value={bookingData.phoneNumber}
                onChange={handleChange}
                placeholder="Nhập số điện thoại"
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label>Ngày nhận phòng</label>
              <input
                type="date"
                name="checkIn"
                value={bookingData.checkIn}
                onChange={handleChange}
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label>Ngày trả phòng</label>
              <input
                type="date"
                name="checkOut"
                value={bookingData.checkOut}
                onChange={handleChange}
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label>Số lượng khách</label>
              <input
                type="number"
                name="guests"
                min="1"
                max="4"
                value={bookingData.guests}
                onChange={handleChange}
                required
              />
            </div>

            <div className={`${styles.formGroup} ${styles['full-width']}`}>
              <label>Yêu cầu đặc biệt</label>
              <textarea
                name="specialRequests"
                value={bookingData.specialRequests}
                onChange={handleChange}
                rows={3}
              />
            </div>

            <button
              type="submit"
              className={styles.submitButton}
              disabled={loading}
            >
              {loading ? 'Đang xử lý...' : 'Xác nhận đặt phòng'}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BookingModal;