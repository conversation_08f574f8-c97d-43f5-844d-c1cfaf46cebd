using Microsoft.AspNetCore.Mvc;
using HOTELSV_BE.Models;
using System.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Authorization;
using System.Data;
using Dapper;
using HOTELSV_BE.Attributes;

namespace HOTELSV_BE.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class RoomsController : ControllerBase
    {
        private readonly string _connectionString;

        public RoomsController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        [HttpGet("GetAllRoom")]
        [RequirePermission("view_rooms")]
        public async Task<ActionResult<IEnumerable<RoomModels>>> GetAllRooms()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var rooms = await connection.QueryAsync<RoomModels>(
                        "sp_GetRoomStatus",
                        commandType: CommandType.StoredProcedure
                    );
                    return Ok(new
                    {
                        success = true,
                        data = rooms
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi khi lấy danh sách phòng",
                    error = ex.Message
                });
            }
        }

        [HttpGet("GetRoomBy/{id}")]
        [RequirePermission("view_rooms")]
        public async Task<ActionResult<RoomModels>> GetRoomById(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var room = await connection.QueryFirstOrDefaultAsync<RoomModels>(
                        "sp_GetRoomById",
                        new { RoomId = id },
                        commandType: CommandType.StoredProcedure
                    );

                    if (room == null)
                        return NotFound(new
                        {
                            success = false,
                            message = $"Không tìm thấy phòng với ID: {id}"
                        });

                    return Ok(new
                    {
                        success = true,
                        data = room
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi khi lấy thông tin phòng",
                    error = ex.Message
                });
            }
        }

        [HttpGet("GetAvailableRoom")]
        [RequirePermission("view_rooms")]
        public async Task<ActionResult<IEnumerable<RoomModels>>> GetAvailableRooms()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var rooms = await connection.QueryAsync<RoomModels>(
                        "sp_GetAvailableRooms",
                        commandType: CommandType.StoredProcedure
                    );
                    return Ok(new
                    {
                        success = true,
                        data = rooms
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi khi lấy danh sách phòng trống",
                    error = ex.Message
                });
            }
        }

        [HttpPost("AddRoom")]
        [RequirePermission("assign_rooms")]
        public async Task<ActionResult> AddRoom(AddRoomModels room)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@RoomNumber", room.RoomNumber);
                    parameters.Add("@RoomTypeId", room.RoomTypeId);
                    parameters.Add("@Floor", room.Floor);
                    parameters.Add("@Status", "Available");
                    parameters.Add("@CleaningStatus", room.CleaningStatus);

                    await connection.ExecuteAsync(
                        "sp_AddRoom",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    return Ok(new
                    {
                        success = true,
                        message = "Thêm phòng thành công",
                        data = room
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi khi thêm phòng",
                    error = ex.Message
                });
            }
        }

        [HttpPut("UpdateRoom/{id}")]
        [RequirePermission("assign_rooms")]
        public async Task<ActionResult> UpdateRoom(int id, UpdateRoomModels room)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@RoomId", id);
                    parameters.Add("@RoomNumber", room.RoomNumber);
                    parameters.Add("@RoomType", room.RoomTypeId);
                    parameters.Add("@Floor", room.Floor);
                    parameters.Add("@Status", room.Status);
                    parameters.Add("@CleaningStatus", room.CleaningStatus);

                    var result = await connection.ExecuteAsync(
                        "sp_UpdateRoom",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (result == 0)
                    {
                        return NotFound(new
                        {
                            success = false,
                            message = $"Không tìm thấy phòng với ID: {id}"
                        });
                    }

                    return Ok(new
                    {
                        success = true,
                        message = "Cập nhật phòng thành công",
                        data = room
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi khi cập nhật phòng",
                    error = ex.Message
                });
            }
        }

        [HttpDelete("Delete/{id}")]
        [RequirePermission("assign_rooms")]
        public async Task<ActionResult> DeleteRoom(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var result = await connection.ExecuteAsync(
                        "sp_DeleteRoom",
                        new { RoomId = id },
                        commandType: CommandType.StoredProcedure
                    );

                    if (result == 0)
                    {
                        return NotFound(new
                        {
                            success = false,
                            message = $"Không tìm thấy phòng với ID: {id}"
                        });
                    }

                    return Ok(new
                    {
                        success = true,
                        message = "Xóa phòng thành công"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi khi xóa phòng",
                    error = ex.Message
                });
            }
        }
        
        [HttpGet]
        public async Task<ActionResult<PaginatedResponse<RoomModels>>> GetRooms([FromQuery] RoomFilterRequest filter)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@PageNumber", filter.PageNumber);
                    parameters.Add("@PageSize", filter.PageSize);
                    parameters.Add("@SearchTerm", filter.SearchTerm);
                    parameters.Add("@RoomTypeId", filter.RoomTypeId);
                    parameters.Add("@Status", filter.Status);
                    parameters.Add("@MinPrice", filter.MinPrice);
                    parameters.Add("@MaxPrice", filter.MaxPrice);
                    parameters.Add("@Floor", filter.Floor);
                    parameters.Add("@SortBy", filter.SortBy ?? "RoomNumber");
                    parameters.Add("@IsAscending", filter.IsAscending);

                    var result = await connection.QueryAsync<RoomModels>(
                        "sp_GetRoomsPaginated",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    var rooms = result.ToList();
                    var totalItems = rooms.Any() ? rooms.First().TotalRecords : 0;

                    var paginatedResponse = new PaginatedResponse<RoomModels>(
                        rooms,
                        totalItems,
                        filter.PageNumber,
                        filter.PageSize
                    );

                    return Ok(new { 
                        success = true,
                        data = paginatedResponse 
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { 
                    success = false,
                    message = "Lỗi khi lấy danh sách phòng",
                    error = ex.Message 
                });
            }
        }
    }
}