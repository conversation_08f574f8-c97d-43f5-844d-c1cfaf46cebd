import publicAxiosInstance from './publicAxiosInstance';

// Public Booking API - không yêu cầu xác thực
const publicBookingApi = {
  addBooking: async (bookingData: any) => {
    try {
      const response = await publicAxiosInstance.post('/Booking/Add', bookingData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể thêm đặt phòng');
    }
  },
};

// Public Room API - không yêu cầu xác thực
const publicRoomApi = {
  getAllRooms: async () => {
    try {
      const response = await publicAxiosInstance.get('/Rooms/GetAllRoom');
      
      // Kiểm tra nếu response.data không có thuộc tính success, thêm vào
      if (response.data && !response.data.hasOwnProperty('success')) {
        return {
          success: true,
          data: response.data
        };
      }
      
      return response.data;
    } catch (error: any) {
      console.error('Error in public getAllRooms:', error);
      throw new Error(error.response?.data?.message || 'Không thể tải danh sách phòng');
    }
  },
  
  getAvailableRooms: async () => {
    try {
      const response = await publicAxiosInstance.get('/Rooms/GetAvailableRoom');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể tải danh sách phòng trống');
    }
  },
  
  getRoomById: async (id: number) => {
    try {
      const response = await publicAxiosInstance.get(`/Rooms/GetRoomBy/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể tải thông tin phòng');
    }
  }
};

export { publicBookingApi, publicRoomApi };
export default publicBookingApi;
