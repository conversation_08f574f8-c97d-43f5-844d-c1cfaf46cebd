import React, { createContext, useState, useContext, useEffect } from 'react';
import { authService, userService } from '../services/apiService';
import { useRouter } from 'next/router';
import { toast } from 'react-toastify';

interface User {
  id: string;
  userId: number; // Thêm userId để phù hợp với API
  userName: string;
  email: string;
  fullName?: string;
  role?: string;
  token?: string;
}

interface LoginCredentials {
  emailOrUsername?: string;
  password: string;
}

interface RegisterData {
  userName: string;
  email: string;
  password: string;
  fullName?: string;
  phoneNumber?: string;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  checkAuthStatus: () => void;
}

const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  user: null,
  loading: true,
  login: async () => {},
  register: async () => {},
  logout: () => {},
  checkAuthStatus: () => {},
});

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Kiểm tra trạng thái xác thực khi khởi động
  const checkAuthStatus = () => {
    setLoading(true);
    const storedUser = localStorage.getItem('user');
    const token = localStorage.getItem('token');

    if (storedUser && token) {
      try {
        const userData = JSON.parse(storedUser);
        setUser(userData);
        setIsAuthenticated(true);

        // Tùy chọn: Xác thực token với server
        // userService.getCurrentUser()
        //   .then(response => {
        //     // Cập nhật thông tin người dùng nếu cần
        //   })
        //   .catch(error => {
        //     // Token không hợp lệ, đăng xuất
        //     logout();
        //   });
      } catch (error) {
        // Lỗi khi parse JSON
        logout();
      }
    }
    setLoading(false);
  };

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const login = async (credentials: LoginCredentials) => {
    setLoading(true);
    try {
      // Xác định xem đầu vào là email hay username
      const isEmail = credentials.emailOrUsername?.includes('@');

      // Tạo credentials mới phù hợp với API
      const apiCredentials = {
        email: isEmail ? credentials.emailOrUsername : '',
        userName: !isEmail ? credentials.emailOrUsername : '',
        password: credentials.password
      };

      console.log('Sending login credentials:', apiCredentials);

      const response = await authService.login(apiCredentials);

      if (response && response.success && response.data) {
        const userData = response.data;

        // Lưu thông tin người dùng và token
        setUser(userData);
        setIsAuthenticated(true);
        localStorage.setItem('user', JSON.stringify(userData));

        if (userData.token) {
          localStorage.setItem('token', userData.token);
        }

        toast.success('Đăng nhập thành công!');
        router.push('/');
      } else {
        throw new Error('Đăng nhập không thành công');
      }
    } catch (error: any) {
      toast.error(error.message || 'Đăng nhập không thành công');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (data: RegisterData) => {
    setLoading(true);
    try {
      const response = await authService.register(data);

      if (response && response.success) {
        toast.success('Đăng ký thành công! Vui lòng đăng nhập.');
        router.push('/auth/login');
      } else {
        throw new Error('Đăng ký không thành công');
      }
    } catch (error: any) {
      toast.error(error.message || 'Đăng ký không thành công');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setUser(null);
    setIsAuthenticated(false);
    router.push('/');
    toast.info('Đã đăng xuất');
  };

  const value = {
    isAuthenticated,
    user,
    loading,
    login,
    register,
    logout,
    checkAuthStatus,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
