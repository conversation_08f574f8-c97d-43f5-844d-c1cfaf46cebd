using Dapper;
using System.Data;
using System.Data.SqlClient;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
namespace HOTELSV_BE.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class RoomTypeController : ControllerBase
    {
        private readonly string _connectionString;

        public RoomTypeController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAll()
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                var roomTypes = await connection.QueryAsync<RoomType>("SELECT * FROM RoomTypes");
                return Ok(roomTypes);
            }
        }

        [HttpGet("GetById/{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                var roomType = await connection.QueryFirstOrDefaultAsync<RoomType>(
                    "SELECT * FROM RoomTypes WHERE RoomTypeId = @Id",
                    new { Id = id });

                if (roomType == null)
                    return NotFound(new { message = "Không tìm thấy loại phòng với ID này." });

                return Ok(roomType);
            }
        }

        [HttpPost("Add")]
        public async Task<IActionResult> Add(AddRoomType addroomType)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@Name", addroomType.Name);
                    parameters.Add("@Description", addroomType.Description);
                    parameters.Add("@BasePrice", addroomType.BasePrice);
                    parameters.Add("@Capacity", addroomType.Capacity);
                    parameters.Add("@BedType", addroomType.BedType);
                    parameters.Add("@Amenities", addroomType.Amenities);
                    parameters.Add("@IsActive", addroomType.IsActive);

                    var result = await connection.QueryFirstOrDefaultAsync<RoomType>(
                        "sp_AddRoomType",
                        parameters,
                        commandType: CommandType.StoredProcedure);

                    return Ok(result);
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "Có lỗi xảy ra khi thêm loại phòng.", error = ex.Message });
            }
        }

        [HttpPut("Update/{id}")]
        public async Task<IActionResult> Update(int id, AddRoomType addroomType)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                var parameters = new DynamicParameters();
                parameters.Add("@RoomTypeId", id);
                parameters.Add("@Name", addroomType.Name);
                parameters.Add("@Description", addroomType.Description);
                parameters.Add("@BasePrice", addroomType.BasePrice);
                parameters.Add("@Capacity", addroomType.Capacity);
                parameters.Add("@BedType", addroomType.BedType);
                parameters.Add("@Amenities", addroomType.Amenities);
                parameters.Add("@IsActive", addroomType.IsActive);

                var result = await connection.QueryFirstOrDefaultAsync<RoomType>(
                    "sp_UpdateRoomType",
                    parameters,
                    commandType: CommandType.StoredProcedure);

                if (result == null)
                    return NotFound(new { message = "Cập nhật loại phòng không thành công" });

                return Ok(result);
            }
        }

        [HttpDelete("Delete/{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                var result = await connection.ExecuteAsync(
                    "DELETE FROM RoomTypes WHERE RoomTypeId = @Id",
                    new { Id = id });

                if (result == 0)
                    return NotFound(new { message = "Xóa loại phòng không thành công" });

                return Ok(new { message = "Xóa loại phòng thành công" });
            }
        }
    
    [HttpGet]
        public async Task<ActionResult<PaginatedResponse<RoomType>>> GetRoomTypes([FromQuery] RoomTypeFilterRequest filter)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@PageNumber", filter.PageNumber);
                    parameters.Add("@PageSize", filter.PageSize);
                    parameters.Add("@SearchTerm", filter.SearchTerm);
                    parameters.Add("@MinPrice", filter.MinPrice);
                    parameters.Add("@MaxPrice", filter.MaxPrice);
                    parameters.Add("@Capacity", filter.Capacity);
                    parameters.Add("@BedType", filter.BedType);
                    parameters.Add("@SortBy", filter.SortBy ?? "Name");
                    parameters.Add("@IsAscending", filter.IsAscending);

                    var result = await connection.QueryAsync<RoomType>(
                        "sp_GetRoomTypesPaginated",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    var roomTypes = result.ToList();
                    var totalItems = roomTypes.Any() ? roomTypes.First().TotalRecords : 0;

                    var paginatedResponse = new PaginatedResponse<RoomType>(
                        roomTypes,
                        totalItems,
                        filter.PageNumber,
                        filter.PageSize
                    );

                    return Ok(new
                    {
                        success = true,
                        data = paginatedResponse
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi khi lấy danh sách loại phòng",
                    error = ex.Message
                });
            }
        }
}
}
