import axios from 'axios';

// Tạo instance axios với cấu hình cơ bản
const api = axios.create({
  baseURL: 'http://localhost:5001/api', // Đổi port thành 5001 theo .env
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // Thêm timeout
});

// Thêm interceptor để xử lý token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// Thêm interceptor để xử lý response
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (!error.response) {
      console.error('Network Error:', error);
      throw new Error('Network error - please check if the server is running');
    }
    
    // <PERSON><PERSON> lý lỗi 401 - Unauthorized
    if (error.response.status === 401) {
      // Nếu ở client-side, xóa token và thông tin người dùng
      if (typeof window !== 'undefined') {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        // Redirect đến trang đăng nhập nếu cần
        if (window.location.pathname !== '/auth/login') {
          window.location.href = '/auth/login';
        }
      }
    }
    
    throw error;
  }
);

// Auth API
export const authApi = {
  login: (credentials: { email?: string; userName?: string; password: string }) => 
    api.post('/Auth/login', credentials),
  
  register: (userData: any) => 
    api.post('/Auth/register', userData),
  
  logout: () => 
    api.post('/Auth/logout')
};

// Room API
export const roomApi = {
  getAllRooms: () => 
    api.get('/Rooms/GetAllRoom'),
  
  getAvailableRooms: () => 
    api.get('/Rooms/GetAvailableRoom'),
  
  getRoomById: (id: number) => 
    api.get(`/Rooms/GetRoomBy/${id}`),
  
  addRoom: (roomData: any) => 
    api.post('/Rooms/AddRoom', roomData),
  
  updateRoom: (id: number, roomData: any) => 
    api.put(`/Rooms/UpdateRoom/${id}`, roomData),
  
  deleteRoom: (id: number) => 
    api.delete(`/Rooms/Delete/${id}`),
  
  getRoomsPaginated: (filter: any) => 
    api.get('/Rooms', { params: filter })
};

// Booking API
export const bookingApi = {
  addBooking: (bookingData: any) => 
    api.post('/Booking/Add', bookingData),
  
  updateBooking: (id: number, bookingData: any) => 
    api.put(`/Booking/Update/${id}`, bookingData),
  
  updateBookingStatus: (id: number, statusData: { status: string, paymentStatus?: string }) => 
    api.put(`/Booking/UpdateStatus/${id}`, statusData),
  
  deleteBooking: (id: number) => 
    api.delete(`/Booking/${id}`),
  
  getBookingDetails: (id: number) => 
    api.get(`/Booking/Details/${id}`),
  
  getAllBookings: () => 
    api.get('/Booking/GetAll'),
  
  getBookingsPaginated: (filter: any) => 
    api.get('/Booking', { params: filter })
};

// Customer API
export const customerApi = {
  getAllCustomers: () => 
    api.get('/Customer/GetAll'),
  
  getCustomerById: (id: number) => 
    api.get(`/Customer/${id}`),
  
  addCustomer: (customerData: any) => 
    api.post('/Customer/Add', customerData),
  
  updateCustomer: (id: number, customerData: any) => 
    api.put(`/Customer/Update/${id}`, customerData),
  
  deleteCustomer: (id: number) => 
    api.delete(`/Customer/Delete/${id}`),
  
  getCustomersPaginated: (filter: any) => 
    api.get('/Customer', { params: filter })
};

// Employee API
export const employeeApi = {
  getAllEmployees: () => 
    api.get('/Employee/GetAll'),
  
  getEmployeeById: (id: number) => 
    api.get(`/Employee/${id}`),
  
  addEmployee: (employeeData: any) => 
    api.post('/Employee/Add', employeeData),
  
  updateEmployee: (id: number, employeeData: any) => 
    api.put(`/Employee/Update/${id}`, employeeData),
  
  deleteEmployee: (id: number) => 
    api.delete(`/Employee/Delete/${id}`),
  
  getEmployeesPaginated: (filter: any) => 
    api.get('/Employee', { params: filter })
};

// Invoice API
export const invoiceApi = {
  getAllInvoices: () => 
    api.get('/Invoice/GetAll'),
  
  getInvoiceById: (id: number) => 
    api.get(`/Invoice/${id}`),
  
  addInvoice: (invoiceData: any) => 
    api.post('/Invoice/Add', invoiceData),
  
  updateInvoice: (id: number, invoiceData: any) => 
    api.put(`/Invoice/Update/${id}`, invoiceData),
  
  deleteInvoice: (id: number) => 
    api.delete(`/Invoice/Delete/${id}`),
  
  getInvoicesPaginated: (filter: any) => 
    api.get('/Invoice', { params: filter })
};

// Payment API
export const paymentApi = {
  getAllPayments: () => 
    api.get('/Payment/GetAll'),
  
  getPaymentById: (id: number) => 
    api.get(`/Payment/${id}`),
  
  addPayment: (paymentData: any) => 
    api.post('/Payment/Add', paymentData),
  
  updatePayment: (id: number, paymentData: any) => 
    api.put(`/Payment/Update/${id}`, paymentData),
  
  deletePayment: (id: number) => 
    api.delete(`/Payment/Delete/${id}`),
  
  getPaymentsPaginated: (filter: any) => 
    api.get('/Payment', { params: filter })
};

// Room Type API
export const roomTypeApi = {
  getAllRoomTypes: () => 
    api.get('/RoomType/GetAll'),
  
  getRoomTypeById: (id: number) => 
    api.get(`/RoomType/${id}`),
  
  addRoomType: (roomTypeData: any) => 
    api.post('/RoomType/Add', roomTypeData),
  
  updateRoomType: (id: number, roomTypeData: any) => 
    api.put(`/RoomType/Update/${id}`, roomTypeData),
  
  deleteRoomType: (id: number) => 
    api.delete(`/RoomType/Delete/${id}`),
  
  getRoomTypesPaginated: (filter: any) => 
    api.get('/RoomType', { params: filter })
};

// Room Service API
export const roomServiceApi = {
  getAllRoomServices: () => 
    api.get('/RoomService/GetAll'),
  
  getRoomServiceById: (id: number) => 
    api.get(`/RoomService/${id}`),
  
  addRoomService: (roomServiceData: any) => 
    api.post('/RoomService/Add', roomServiceData),
  
  updateRoomService: (id: number, roomServiceData: any) => 
    api.put(`/RoomService/Update/${id}`, roomServiceData),
  
  deleteRoomService: (id: number) => 
    api.delete(`/RoomService/Delete/${id}`),
  
  getRoomServicesPaginated: (filter: any) => 
    api.get('/RoomService', { params: filter })
};

// Booking Service API
export const bookingServiceApi = {
  getAllBookingServices: () => 
    api.get('/BookingService/GetAll'),
  
  getBookingServiceById: (id: number) => 
    api.get(`/BookingService/${id}`),
  
  addBookingService: (bookingServiceData: any) => 
    api.post('/BookingService/Add', bookingServiceData),
  
  updateBookingService: (id: number, bookingServiceData: any) => 
    api.put(`/BookingService/Update/${id}`, bookingServiceData),
  
  deleteBookingService: (id: number) => 
    api.delete(`/BookingService/Delete/${id}`),
  
  getBookingServicesPaginated: (filter: any) => 
    api.get('/BookingService', { params: filter })
};

// User API
export const userApi = {
  getAllUsers: () => 
    api.get('/User/GetAll'),
  
  getUserById: (id: number) => 
    api.get(`/User/${id}`),
  
  addUser: (userData: any) => 
    api.post('/User/Add', userData),
  
  updateUser: (id: number, userData: any) => 
    api.put(`/User/Update/${id}`, userData),
  
  deleteUser: (id: number) => 
    api.delete(`/User/Delete/${id}`),
  
  getUsersPaginated: (filter: any) => 
    api.get('/User', { params: filter })
};

// Role API
export const roleApi = {
  getAllRoles: () => 
    api.get('/Role/GetAll'),
  
  getRoleById: (id: number) => 
    api.get(`/Role/${id}`),
  
  addRole: (roleData: any) => 
    api.post('/Role/Add', roleData),
  
  updateRole: (id: number, roleData: any) => 
    api.put(`/Role/Update/${id}`, roleData),
  
  deleteRole: (id: number) => 
    api.delete(`/Role/Delete/${id}`),
  
  getRolesPaginated: (filter: any) => 
    api.get('/Role', { params: filter })
};

// Permission API
export const permissionApi = {
  getAllPermissions: () => 
    api.get('/Permission/GetAll'),
  
  getPermissionById: (id: number) => 
    api.get(`/Permission/${id}`),
  
  addPermission: (permissionData: any) => 
    api.post('/Permission/Add', permissionData),
  
  updatePermission: (id: number, permissionData: any) => 
    api.put(`/Permission/Update/${id}`, permissionData),
  
  deletePermission: (id: number) => 
    api.delete(`/Permission/Delete/${id}`),
  
  getPermissionsPaginated: (filter: any) => 
    api.get('/Permission', { params: filter })
};

// User Role API
export const userRoleApi = {
  getAllUserRoles: () => 
    api.get('/UserRole/GetAll'),
  
  getUserRoleById: (id: number) => 
    api.get(`/UserRole/${id}`),
  
  addUserRole: (userRoleData: any) => 
    api.post('/UserRole/Add', userRoleData),
  
  updateUserRole: (id: number, userRoleData: any) => 
    api.put(`/UserRole/Update/${id}`, userRoleData),
  
  deleteUserRole: (id: number) => 
    api.delete(`/UserRole/Delete/${id}`),
  
  getUserRolesPaginated: (filter: any) => 
    api.get('/UserRole', { params: filter })
};

// Role Permission API
export const rolePermissionApi = {
  getAllRolePermissions: () => 
    api.get('/RolePermission/GetAll'),
  
  getRolePermissionById: (id: number) => 
    api.get(`/RolePermission/${id}`),
  
  addRolePermission: (rolePermissionData: any) => 
    api.post('/RolePermission/Add', rolePermissionData),
  
  updateRolePermission: (id: number, rolePermissionData: any) => 
    api.put(`/RolePermission/Update/${id}`, rolePermissionData),
  
  deleteRolePermission: (id: number) => 
    api.delete(`/RolePermission/Delete/${id}`),
  
  getRolePermissionsPaginated: (filter: any) => 
    api.get('/RolePermission', { params: filter })
};

export default api;
