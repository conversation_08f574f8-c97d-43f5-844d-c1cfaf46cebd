using System;
using System.Data.SqlClient;

namespace Data
{
    public static class TestConnection
    {
        public static void Run(string connectionString)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    Console.WriteLine("Kết nối database thành công!");
                    
                    // Kiểm tra version của SQL Server
                    string version = connection.ServerVersion;
                    Console.WriteLine($"SQL Server Version: {version}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi kết nối: {ex.Message}");
            }
        }
    }
} 