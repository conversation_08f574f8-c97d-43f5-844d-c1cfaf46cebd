import { roomApi } from './api';

const roomService = {
  async getAllRooms() {
    try {
      const response = await roomApi.getAllRooms();
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getAvailableRooms() {
    try {
      const response = await roomApi.getAvailableRooms();
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách phòng trống');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getRoomById(id: number) {
    try {
      const response = await roomApi.getRoomById(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải thông tin phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async addRoom(roomData: any) {
    try {
      const response = await roomApi.addRoom(roomData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể thêm phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async updateRoom(id: number, roomData: any) {
    try {
      const response = await roomApi.updateRoom(id, roomData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể cập nhật phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async deleteRoom(id: number) {
    try {
      const response = await roomApi.deleteRoom(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể xóa phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getRoomsPaginated(filter: any) {
    try {
      const response = await roomApi.getRoomsPaginated(filter);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  }
};

export { roomService };
