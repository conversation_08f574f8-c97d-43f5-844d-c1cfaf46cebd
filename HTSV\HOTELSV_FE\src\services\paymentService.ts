import { paymentApi } from './api';

const paymentService = {
  async getAllPayments() {
    try {
      const response = await paymentApi.getAllPayments();
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || '<PERSON>hông thể tải danh sách thanh toán');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getPaymentById(id: number) {
    try {
      const response = await paymentApi.getPaymentById(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải thông tin thanh toán');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async addPayment(paymentData: any) {
    try {
      const response = await paymentApi.addPayment(paymentData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || '<PERSON>hông thể thêm thanh toán');
      }
      throw new Error('<PERSON>hông thể kết nối đến server');
    }
  },

  async updatePayment(id: number, paymentData: any) {
    try {
      const response = await paymentApi.updatePayment(id, paymentData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể cập nhật thanh toán');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async deletePayment(id: number) {
    try {
      const response = await paymentApi.deletePayment(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể xóa thanh toán');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getPaymentsPaginated(filter: any) {
    try {
      const response = await paymentApi.getPaymentsPaginated(filter);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách thanh toán');
      }
      throw new Error('Không thể kết nối đến server');
    }
  }
};

export { paymentService };