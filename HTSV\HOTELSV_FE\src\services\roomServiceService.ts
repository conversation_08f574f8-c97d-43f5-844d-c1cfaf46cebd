import { roomServiceApi } from './api';

const roomServiceService = {
  async getAllRoomServices() {
    try {
      const response = await roomServiceApi.getAllRoomServices();
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách dịch vụ phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getRoomServiceById(id: number) {
    try {
      const response = await roomServiceApi.getRoomServiceById(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải thông tin dịch vụ phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async addRoomService(roomServiceData: any) {
    try {
      const response = await roomServiceApi.addRoomService(roomServiceData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể thêm dịch vụ phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async updateRoomService(id: number, roomServiceData: any) {
    try {
      const response = await roomServiceApi.updateRoomService(id, roomServiceData);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể cập nhật dịch vụ phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async deleteRoomService(id: number) {
    try {
      const response = await roomServiceApi.deleteRoomService(id);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể xóa dịch vụ phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  },

  async getRoomServicesPaginated(filter: any) {
    try {
      const response = await roomServiceApi.getRoomServicesPaginated(filter);
      return response.data;
    } catch (error: any) {
      if (error.response) {
        throw new Error(error.response.data.message || 'Không thể tải danh sách dịch vụ phòng');
      }
      throw new Error('Không thể kết nối đến server');
    }
  }
};

export { roomServiceService };