import React from 'react';
import Head from 'next/head';
import { useAuth } from '../../contexts/AuthContext';

export default function AdminTest() {
  const { isAuthenticated, user } = useAuth();

  return (
    <>
      <Head>
        <title>Admin Test | Hotel Nhóm 1</title>
      </Head>
      <div style={{ padding: '2rem' }}>
        <h1>Admin Test Page</h1>
        <p>Kiểm tra xác thực: {isAuthenticated ? 'Đã đăng nhập' : 'Chưa đăng nhập'}</p>
        {user && (
          <div>
            <h2>Thông tin người dùng:</h2>
            <pre>{JSON.stringify(user, null, 2)}</pre>
          </div>
        )}
      </div>
    </>
  );
}
