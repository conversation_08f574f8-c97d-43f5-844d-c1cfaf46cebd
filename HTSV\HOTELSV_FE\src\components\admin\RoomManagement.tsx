import React, { useState, useEffect } from 'react';
import { roomApi, roomTypeApi } from '../../services/adminService';
import styles from '../../styles/admin.module.css';
import { FaEdit, FaTrash, FaPlus, FaSearch } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';

interface Room {
  roomId: number;
  roomNumber: string;
  roomTypeId: number;
  roomType: string;
  floor: number;
  status: string;
  cleaningStatus: string;
  basePrice: number;
}

interface RoomType {
  roomTypeId: number;
  name: string;
  basePrice: number;
}

const RoomManagement: React.FC = () => {
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const [rooms, setRooms] = useState<Room[]>([]);
  const [roomTypes, setRoomTypes] = useState<RoomType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [currentRoom, setCurrentRoom] = useState<Room | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const [formData, setFormData] = useState({
    roomNumber: '',
    roomTypeId: 1, // Default to 1 instead of 0 to ensure a valid selection
    floor: 1,
    cleaningStatus: 'Clean'
  });

  useEffect(() => {
    // Check if user is authenticated before fetching data
    if (!isAuthenticated) {
      toast.error('Bạn cần đăng nhập để truy cập trang quản trị');
      router.push('/auth/login?redirect=/admin/rooms');
      return;
    }
    
    // Check if user has admin role
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    if (!user.role || user.role !== 'Admin') {
      toast.error('Bạn không có quyền truy cập trang quản trị');
      router.push('/');
      return;
    }
    
    // Fetch data if user is authenticated and has admin role
    fetchRooms();
    fetchRoomTypes();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, router]);

  const fetchRooms = async () => {
    try {
      setLoading(true);

      // Thêm timestamp để tránh cache
      const timestamp = new Date().getTime();
      console.log(`Fetching rooms at timestamp: ${timestamp}`);

      // Admin users should only use the authenticated API
      const response = await roomApi.getAllRooms();
      console.log('Rooms response in component:', response);

      // Kiểm tra cấu trúc dữ liệu
      if (response.success && Array.isArray(response.data)) {
        console.log('Setting rooms from response.data:', response.data);
        setRooms(response.data);
      } else if (Array.isArray(response)) {
        console.log('Setting rooms from response array:', response);
        setRooms(response);
      } else {
        console.error('Unexpected rooms data structure:', response);
        toast.error('Không thể tải danh sách phòng');
      }
    } catch (error: any) {
      console.error('Error fetching rooms:', error);
      
      // For admin pages, we should not fall back to public API
      // Instead, show an error message and potentially redirect to login
      toast.error('Không thể tải danh sách phòng. Vui lòng đăng nhập lại.');
      
      // Check if error is due to authentication
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        // Clear authentication data and redirect to login
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        router.push('/auth/login?redirect=/admin/rooms');
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchRoomTypes = async () => {
    try {
      const response = await roomTypeApi.getAllRoomTypes();
      console.log('Room types response in component:', response);

      // Kiểm tra cấu trúc dữ liệu
      if (response.success && Array.isArray(response.data)) {
        setRoomTypes(response.data);
      } else if (Array.isArray(response)) {
        setRoomTypes(response);
      } else {
        console.error('Unexpected room types data structure:', response);
        toast.error('Không thể tải danh sách loại phòng');
      }
    } catch (error: any) {
      console.error('Error fetching room types:', error);
      toast.error(error.message || 'Không thể tải danh sách loại phòng');
      
      // Check if error is due to authentication
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        // Clear authentication data and redirect to login
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        router.push('/auth/login?redirect=/admin/rooms');
      }
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'roomTypeId' || name === 'floor' ? parseInt(value) : value
    });
  };

  const handleAddRoom = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      console.log('Submitting room data:', formData);

      // Đảm bảo roomTypeId là số
      const dataToSubmit = {
        ...formData,
        roomTypeId: parseInt(formData.roomTypeId.toString()),
        floor: parseInt(formData.floor.toString())
      };

      const response = await roomApi.addRoom(dataToSubmit);
      console.log('Add room response in component:', response);

      if (response.success) {
        toast.success('Thêm phòng thành công');

        // Thêm phòng mới vào state
        const selectedRoomType = roomTypes.find(t => t.roomTypeId === dataToSubmit.roomTypeId);
        const newRoom = {
          roomId: response.data?.roomId || Math.floor(Math.random() * 1000) + 100,
          roomNumber: dataToSubmit.roomNumber,
          roomTypeId: dataToSubmit.roomTypeId,
          roomType: selectedRoomType?.name || 'Standard',
          floor: dataToSubmit.floor,
          status: 'Available', // Mặc định là Available
          cleaningStatus: dataToSubmit.cleaningStatus,
          basePrice: selectedRoomType?.basePrice || 1000000 // Default price if not found
        };

        setRooms(prevRooms => [...prevRooms, newRoom]);
        setShowModal(false);
        resetForm();
      } else {
        toast.error(response.message || 'Không thể thêm phòng');
      }
    } catch (error: any) {
      console.error('Error adding room:', error);
      toast.error(error.message || 'Không thể thêm phòng');
    }
  };

  const handleUpdateRoom = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentRoom) return;

    try {
      console.log('Updating room data:', formData);

      // Đảm bảo roomTypeId là số
      const dataToSubmit = {
        ...formData,
        roomTypeId: parseInt(formData.roomTypeId.toString()),
        floor: parseInt(formData.floor.toString())
      };

      const response = await roomApi.updateRoom(currentRoom.roomId, dataToSubmit);
      console.log('Update room response in component:', response);

      if (response.success) {
        toast.success('Cập nhật phòng thành công');

        // Cập nhật phòng trong state
        const selectedRoomType = roomTypes.find(t => t.roomTypeId === dataToSubmit.roomTypeId);
        setRooms(prevRooms => prevRooms.map(room => {
          if (room.roomId === currentRoom.roomId) {
            return {
              ...room,
              roomNumber: dataToSubmit.roomNumber,
              roomTypeId: dataToSubmit.roomTypeId,
              roomType: selectedRoomType?.name || room.roomType,
              floor: dataToSubmit.floor,
              cleaningStatus: dataToSubmit.cleaningStatus,
              basePrice: selectedRoomType?.basePrice || room.basePrice || 1000000
            };
          }
          return room;
        }));

        setShowModal(false);
        resetForm();
      } else {
        toast.error(response.message || 'Không thể cập nhật phòng');
      }
    } catch (error: any) {
      console.error('Error updating room:', error);
      toast.error(error.message || 'Không thể cập nhật phòng');
    }
  };

  const handleDeleteRoom = async (id: number) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa phòng này?')) {
      try {
        console.log('Deleting room with ID:', id);

        // Thêm log để kiểm tra trước khi gọi API
        console.log('Before API call - Current rooms:', rooms);

        const response = await roomApi.deleteRoom(id);
        console.log('Delete room response in component:', response);

        if (response.success) {
          toast.success('Xóa phòng thành công');

          // Xóa phòng khỏi state trước khi gọi lại API
          setRooms(prevRooms => {
            const filteredRooms = prevRooms.filter(room => room.roomId !== id);
            console.log('After filtering rooms:', filteredRooms);
            return filteredRooms;
          });

          // Sau đó gọi lại API để đảm bảo dữ liệu đồng bộ
          console.log('Refreshing rooms from API after delete');
          await fetchRooms();
        } else {
          toast.error(response.message || 'Không thể xóa phòng');
        }
      } catch (error: any) {
        console.error('Error deleting room:', error);
        toast.error(error.message || 'Không thể xóa phòng');

        // Không xóa phòng khỏi state khi có lỗi
        // Thay vào đó, gọi lại API để đảm bảo dữ liệu đồng bộ
        try {
          console.log('Refreshing rooms after error');
          await fetchRooms();
        } catch (refreshError) {
          console.error('Error refreshing rooms after delete error:', refreshError);
        }
      }
    }
  };

  const openEditModal = (room: Room) => {
    setCurrentRoom(room);
    setFormData({
      roomNumber: room.roomNumber,
      roomTypeId: room.roomTypeId,
      floor: room.floor,
      cleaningStatus: room.cleaningStatus
    });
    setIsEditing(true);
    setShowModal(true);
  };

  const openAddModal = () => {
    resetForm();
    setIsEditing(false);
    setShowModal(true);
  };

  const resetForm = () => {
    setFormData({
      roomNumber: '',
      roomTypeId: roomTypes.length > 0 ? roomTypes[0].roomTypeId : 1, // Default to 1 if no room types
      floor: 1,
      cleaningStatus: 'Clean'
    });
    setCurrentRoom(null);
    console.log('Form reset with roomTypes:', roomTypes);
  };

  const filteredRooms = rooms.filter(room => {
    const matchesSearch = room.roomNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         room.roomType.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || room.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  return (
    <div>
      <div className={styles.toolbarSection}>
        <div className={styles.searchBox}>
          <FaSearch />
          <input
            type="text"
            placeholder="Tìm kiếm phòng..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className={styles.filterContainer}>
          <select
            className={styles.filterSelect}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">Tất cả trạng thái</option>
            <option value="Available">Trống</option>
            <option value="Booked">Đã đặt</option>
            <option value="Maintenance">Bảo trì</option>
          </select>
          <button className={`${styles.button} ${styles.primaryButton}`} onClick={openAddModal}>
            <FaPlus /> Thêm phòng
          </button>
        </div>
      </div>

      {loading ? (
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
        </div>
      ) : (
        <div className={styles.tableContainer}>
          <table className={styles.dataTable}>
            <thead>
              <tr>
                <th>ID</th>
                <th>Số phòng</th>
                <th>Loại phòng</th>
                <th>Tầng</th>
                <th>Trạng thái</th>
                <th>Tình trạng vệ sinh</th>
                <th>Giá cơ bản</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {filteredRooms.length > 0 ? (
                filteredRooms.map((room) => (
                  <tr key={room.roomId}>
                    <td>{room.roomId}</td>
                    <td>{room.roomNumber}</td>
                    <td>{room.roomType}</td>
                    <td>{room.floor}</td>
                    <td>
                      <span className={`${styles.statusBadge} ${styles[`status${room.status}`]}`}>
                        {room.status}
                      </span>
                    </td>
                    <td>{room.cleaningStatus}</td>
                    <td>{(room.basePrice || 0).toLocaleString()} VND</td>
                    <td>
                      <button
                        className={`${styles.actionButton} ${styles.editButton}`}
                        onClick={() => openEditModal(room)}
                      >
                        <FaEdit />
                      </button>
                      <button
                        className={`${styles.actionButton} ${styles.deleteButton}`}
                        onClick={() => handleDeleteRoom(room.roomId)}
                      >
                        <FaTrash />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={8} style={{ textAlign: 'center' }}>
                    Không tìm thấy phòng nào
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* Modal for adding/editing room */}
      {showModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h2 className={styles.modalTitle}>
                {isEditing ? 'Chỉnh sửa phòng' : 'Thêm phòng mới'}
              </h2>
              <button className={styles.closeButton} onClick={() => setShowModal(false)}>
                &times;
              </button>
            </div>
            <form onSubmit={isEditing ? handleUpdateRoom : handleAddRoom}>
              <div className={styles.formGroup}>
                <label>Số phòng</label>
                <input
                  type="text"
                  name="roomNumber"
                  value={formData.roomNumber}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>Loại phòng</label>
                <select
                  name="roomTypeId"
                  value={formData.roomTypeId}
                  onChange={handleInputChange}
                  required
                >
                  {roomTypes && roomTypes.length > 0 ? (
                    roomTypes.map((type) => (
                      <option key={type.roomTypeId} value={type.roomTypeId}>
                        {type.name}
                      </option>
                    ))
                  ) : (
                    <>
                      <option value="1">Standard</option>
                      <option value="2">Deluxe</option>
                      <option value="3">Suite</option>
                    </>
                  )}
                </select>
              </div>
              <div className={styles.formGroup}>
                <label>Tầng</label>
                <input
                  type="number"
                  name="floor"
                  min="1"
                  value={formData.floor}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className={styles.formGroup}>
                <label>Tình trạng vệ sinh</label>
                <select
                  name="cleaningStatus"
                  value={formData.cleaningStatus}
                  onChange={handleInputChange}
                  required
                >
                  <option value="Clean">Sạch sẽ</option>
                  <option value="Dirty">Cần dọn dẹp</option>
                  <option value="Cleaning">Đang dọn dẹp</option>
                </select>
              </div>
              <div className={styles.buttonContainer}>
                <button
                  type="button"
                  className={`${styles.button} ${styles.secondaryButton}`}
                  onClick={() => setShowModal(false)}
                >
                  Hủy
                </button>
                <button type="submit" className={`${styles.button} ${styles.primaryButton}`}>
                  {isEditing ? 'Cập nhật' : 'Thêm mới'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default RoomManagement;
