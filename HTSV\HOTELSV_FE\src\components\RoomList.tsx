import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
// import { roomService } from '../services/apiService'; // Không sử dụng nữa
import { publicRoomApi } from '../services/publicService'; // Sử dụng public API không cần xác thực
import { Room } from '../types/room';
import styles from '../styles/rooms.module.css';
import BookingModal from './BookingModal';
import { toast } from 'react-toastify';
import Image from 'next/image';
import { useAuth } from '../contexts/AuthContext';

const RoomList = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [filter, setFilter] = useState('all'); // all, available, booked

  useEffect(() => {
    const fetchRooms = async () => {
      try {
        setLoading(true);
        setError('');

        // Sử dụng public API để lấy danh sách phòng
        console.log('Fetching rooms using public API');
        const response = await publicRoomApi.getAllRooms();
        
        if (response.success && Array.isArray(response.data)) {
          setRooms(response.data);
        } else if (Array.isArray(response)) {
          setRooms(response);
        } else {
          console.error('Unexpected data format:', response);
          toast.error('Định dạng dữ liệu không hợp lệ. Vui lòng thử lại sau.');
          setError('Không thể tải danh sách phòng. Định dạng dữ liệu không hợp lệ.');
        }
      } catch (error: any) {
        console.error('Error in RoomList:', error);
        toast.error(error.message || 'Không thể tải danh sách phòng');
        setError('Không thể tải danh sách phòng. Vui lòng thử lại sau.');
        
        // Retry once after a short delay
        setTimeout(async () => {
          try {
            const retryResponse = await publicRoomApi.getAllRooms();
            if (retryResponse.success && Array.isArray(retryResponse.data)) {
              setRooms(retryResponse.data);
              setError('');
            } else if (Array.isArray(retryResponse)) {
              setRooms(retryResponse);
              setError('');
            } else {
              console.error('Retry failed with unexpected data format:', retryResponse);
              setError('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng và thử lại sau.');
            }
          } catch (retryError) {
            console.error('Retry failed:', retryError);
            setError('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng và thử lại sau.');
          } finally {
            setLoading(false);
          }
        }, 2000);
        return; // Skip the final setLoading(false) as it will be called after retry
      } finally {
        setLoading(false);
      }
    };

    fetchRooms();
  }, []);

  const handleBooking = (room: Room) => {
    // Kiểm tra xem người dùng đã đăng nhập chưa
    if (!isAuthenticated) {
      toast.info('Vui lòng đăng nhập để đặt phòng');
      // Lưu thông tin phòng vào localStorage để sau khi đăng nhập có thể quay lại
      localStorage.setItem('selectedRoom', JSON.stringify(room));
      router.push('/auth/login?redirect=/room/room');
      return;
    }

    setSelectedRoom(room);
    setIsModalOpen(true);
  };

  const filteredRooms = rooms.filter(room => {
    if (filter === 'available') return room.status === 'Available';
    if (filter === 'booked') return room.status === 'Booked';
    return true;
  });

  if (loading) return (
    <div className={styles.loading}>
      <div className={styles.loadingSpinner}></div>
      <p>Đang tải danh sách phòng...</p>
    </div>
  );
  
  if (error) return (
    <div className={styles.error}>
      <p>{error}</p>
      <button 
        className={styles.retryButton}
        onClick={() => window.location.reload()}
      >
        Thử lại
      </button>
    </div>
  );

  return (
    <div className={styles.roomsContainer}>
      <div className={styles.filterTabs}>
        <button
          className={`${styles.filterTab} ${filter === 'all' ? styles.active : ''}`}
          onClick={() => setFilter('all')}
        >
          Tất cả phòng
        </button>
        <button
          className={`${styles.filterTab} ${filter === 'available' ? styles.active : ''}`}
          onClick={() => setFilter('available')}
        >
          Phòng có sẵn
        </button>
        <button
          className={`${styles.filterTab} ${filter === 'booked' ? styles.active : ''}`}
          onClick={() => setFilter('booked')}
        >
          Phòng đã đặt
        </button>
      </div>

      <div className={styles.roomsGrid}>
        {filteredRooms.map((room) => (
          <div key={room.roomId} className={styles.roomCard}>
            <div className={styles.roomImageContainer}>
              <Image
                src={`/rooms/${(room.roomType || 'standard').toLowerCase()}.jpg`}
                alt={room.roomType || 'Room'}
                layout="fill"
                objectFit="cover"
                className={styles.roomImage}
              />
              <div className={styles.roomStatus}>
                <span className={`${styles.statusBadge} ${styles[(room.status || 'available').toLowerCase()]}`}>
                  {room.status || 'Available'}
                </span>
              </div>
            </div>

            <div className={styles.roomContent}>
              <h3 className={styles.roomName}>
                {room.roomType || 'Standard'} - Room {room.roomNumber}
              </h3>

              <div className={styles.roomFeatures}>
                <span className={styles.feature}>Floor {room.floor || '1'}</span>
                <span className={styles.feature}>{room.bedType || 'Single'}</span>
                <span className={styles.feature}>{room.capacity || '2'} Guests</span>
              </div>

              <div className={styles.roomDescription}>
                <p>{room.roomTypeDescription || 'Comfortable room with all basic amenities'}</p>
              </div>

              <div className={styles.amenities}>
                {room.amenities ? room.amenities.split(',').map((amenity, index) => (
                  <span key={index} className={styles.amenity}>{amenity.trim()}</span>
                )) : (
                  <span className={styles.amenity}>Basic Amenities</span>
                )}
              </div>

              <div className={styles.roomFooter}>
                <div className={styles.priceInfo}>
                  <span className={styles.priceLabel}>Per Night</span>
                  <span className={styles.priceValue}>{room.price ? `${room.price.toLocaleString()} VND` : '500,000 VND'}</span>
                </div>
                <div className={styles.roomActions}>
                  <button
                    className={styles.detailButton}
                    onClick={() => router.push(`/room/${room.roomId}`)}
                  >
                    Chi tiết
                  </button>
                  {(room.status === 'Available' || !room.status) && (
                    <button
                      className={styles.bookButton}
                      onClick={() => handleBooking(room)}
                    >
                      Đặt phòng
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {selectedRoom && (
        <BookingModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          room={{
            name: `${selectedRoom.roomType || 'Standard'} - Room ${selectedRoom.roomNumber}`,
            price: `${selectedRoom.price ? selectedRoom.price.toLocaleString() : '500,000'} VND/night`,
            image: `/rooms/${(selectedRoom.roomType || 'standard').toLowerCase()}.jpg`,
            roomId: selectedRoom.roomId,
            roomTypeId: selectedRoom.roomTypeId || 1
          }}
        />
      )}
    </div>
  );
};

export default RoomList;
