﻿CREATE PROCEDURE [dbo].[GetAllEmployees]
AS
BEGIN
    SET NOCOUNT ON;

    SELECT 
        EmployeeId,
        UserId,
        Position,
        Department,
        HireDate,
        Salary,
        ManagerId,
        IsActive
    FROM [dbo].[Employees];
END;
GO
CREATE PROCEDURE [dbo].[GetEmployeeById]
    @EmployeeId INT
AS
BEGIN
    SET NOCOUNT ON;

    SELECT 
        EmployeeId,
        UserId,
        Position,
        Department,
        HireDate,
        Salary,
        ManagerId,
        IsActive
    FROM [dbo].[Employees]
    WHERE EmployeeId = @EmployeeId;
END;
GO
USE [data_QLKS_113_Nhom1]
GO
/****** Object:  StoredProcedure [dbo].[sp_AddEmployee]    Script Date: 5/20/2025 9:41:26 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:      <Nhóm 1 ( Tuấn đẹp trai vãi)>
-- =============================================

ALTER PROCEDURE [dbo].[sp_AddEmployee]
    @Username NVARCHAR(50),
    @PasswordHash NVARCHAR(200),
    @Email NVARCHAR(100),
    @FirstName NVARCHAR(50),
    @LastName NVARCHAR(50),
    @Phone NVARCHAR(20),
    @Position NVARCHAR(50),
    @Department NVARCHAR(50),
    @Salary DECIMAL(18,2),
    @ManagerId INT = NULL
AS
BEGIN
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Thêm user mới
        INSERT INTO Users (Username, PasswordHash, Email, FirstName, LastName, Phone, IsActive, CreatedDate)
        VALUES (@Username, @PasswordHash, @Email, @FirstName, @LastName, @Phone, 1, GETDATE());
        
        DECLARE @UserId INT = SCOPE_IDENTITY();
        
        -- Gán role dựa vào department
        DECLARE @RoleId INT;
        SELECT @RoleId = RoleId FROM Roles WHERE RoleName = 
            CASE @Department
                WHEN 'Quản lý' THEN 'Manager'
                WHEN 'Lễ tân' THEN 'Receptionist'
                WHEN 'Dịch vụ phòng' THEN 'Housekeeper'
                WHEN 'Kỹ thuật' THEN 'Maintenance'
                ELSE 'Receptionist'
            END;
            
        INSERT INTO UserRoles (UserId, RoleId) VALUES (@UserId, @RoleId);
        
        -- Thêm thông tin nhân viên
        INSERT INTO Employees (UserId, Position, Department, HireDate, Salary, ManagerId, IsActive)
        VALUES (@UserId, @Position, @Department, GETDATE(), @Salary, @ManagerId, 1);
        
        DECLARE @EmployeeId INT = SCOPE_IDENTITY();
        
        COMMIT TRANSACTION;
        SELECT @EmployeeId AS EmployeeId;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        SELECT ERROR_MESSAGE() AS ErrorMessage;
    END CATCH
END
USE [data_QLKS_113_Nhom1]
GO
/****** Object:  StoredProcedure [dbo].[sp_DeleteEmployee]    Script Date: 5/20/2025 9:41:38 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:      <Nhóm 1 ( Tuấn đẹp trai vãi)>
-- =============================================
ALTER PROCEDURE [dbo].[sp_DeleteEmployee]
    @EmployeeId INT
AS
BEGIN
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Lưu UserId để xóa sau
        DECLARE @UserId INT;
        SELECT @UserId = UserId FROM Employees WHERE EmployeeId = @EmployeeId;
        
        -- Cập nhật ManagerId cho các nhân viên khác
        UPDATE Employees SET ManagerId = NULL WHERE ManagerId = @EmployeeId;
        
        -- Xóa thông tin nhân viên
        DELETE FROM Employees WHERE EmployeeId = @EmployeeId;
        
        -- Xóa thông tin user
        DELETE FROM UserRoles WHERE UserId = @UserId;
        DELETE FROM Users WHERE UserId = @UserId;
        
        COMMIT TRANSACTION;
        SELECT 'Employee deleted successfully' AS Message;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        SELECT ERROR_MESSAGE() AS ErrorMessage;
    END CATCH
END
USE [data_QLKS_113_Nhom1]
GO
/****** Object:  StoredProcedure [dbo].[sp_UpdateEmployee]    Script Date: 5/20/2025 9:42:02 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[sp_UpdateEmployee]
    @EmployeeId INT,
    @Position NVARCHAR(50) = NULL,
    @Department NVARCHAR(50) = NULL,
    @Salary DECIMAL(18,2) = NULL,
    @ManagerId INT = NULL,
    @IsActive BIT = NULL
AS
BEGIN
    BEGIN TRY
        BEGIN TRANSACTION;
        
        UPDATE Employees
        SET Position = ISNULL(@Position, Position),
            Department = ISNULL(@Department, Department),
            Salary = ISNULL(@Salary, Salary),
            ManagerId = ISNULL(@ManagerId, ManagerId),
            IsActive = ISNULL(@IsActive, IsActive)
        WHERE EmployeeId = @EmployeeId;
        
        -- Trả về thông tin nhân viên đã cập nhật
        SELECT 
            e.EmployeeId, 
            e.UserId, 
            e.Position, 
            e.Department, 
            e.HireDate, 
            e.Salary, 
            e.ManagerId, 
            e.IsActive,
            u.Username, 
            u.Email, 
            u.FirstName, 
            u.LastName, 
            u.Phone,
            mu.FirstName + ' ' + mu.LastName AS ManagerName  -- Sửa từ m.FirstName thành mu.FirstName
        FROM Employees e
        JOIN Users u ON e.UserId = u.UserId
        LEFT JOIN Employees m ON e.ManagerId = m.EmployeeId
        LEFT JOIN Users mu ON m.UserId = mu.UserId
        WHERE e.EmployeeId = @EmployeeId;
        
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        SELECT ERROR_MESSAGE() AS ErrorMessage;
    END CATCH
END