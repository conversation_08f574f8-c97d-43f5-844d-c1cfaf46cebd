namespace HOTELSV_BE.Models
{
    public class PaymentModels
    {
        public int PaymentId { get; set; }
        public int InvoiceId { get; set; }
        public decimal Amount { get; set; }
        public DateTime PaymentDate { get; set; }
        public string PaymentMethod { get; set; }
        public string Status { get; set; }
        public string TransactionId { get; set; }
        public string ReceiptNumber { get; set; }
        public int ProcessedBy { get; set; }
    }

    public class AddPaymentModels
    {
        public int InvoiceId { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; }
        public string TransactionId { get; set; }
        public string ReceiptNumber { get; set; }
        public int ProcessedBy { get; set; }
    }

    public class UpdatePaymentModels
    {
        public int InvoiceId { get; set; }
        public decimal Amount { get; set; }
        public DateTime PaymentDate { get; set; }
        public string PaymentMethod { get; set; }
        public string Status { get; set; }
        public string TransactionId { get; set; }
        public string ReceiptNumber { get; set; }
        public int ProcessedBy { get; set; }
    }
}