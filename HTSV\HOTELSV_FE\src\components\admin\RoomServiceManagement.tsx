import React, { useState, useEffect } from 'react';
import { roomServiceApi } from '../../services/adminService';
import styles from '../../styles/admin.module.css';
import { FaEdit, FaTrash, FaSearch, FaPlus } from 'react-icons/fa';
import { toast } from 'react-toastify';

interface RoomService {
  serviceId: number;
  name: string;
  description: string;
  price: number;
  isActive: boolean;
  category: string;
}

const RoomServiceManagement = () => {
  const [services, setServices] = useState<RoomService[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedService, setSelectedService] = useState<RoomService | null>(null);
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: 0,
    isActive: true,
    category: 'Dịch vụ phòng'
  });

  useEffect(() => {
    fetchServices();
  }, []);

  const fetchServices = async () => {
    try {
      setLoading(true);
      const response = await roomServiceApi.getAllRoomServices();
      if (response.success) {
        setServices(response.data);
      }
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    if (type === 'checkbox') {
      const { checked } = e.target as HTMLInputElement;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const openAddModal = () => {
    setFormData({
      name: '',
      description: '',
      price: 0,
      isActive: true,
      category: 'Dịch vụ phòng'
    });
    setIsAddModalOpen(true);
  };

  const openEditModal = (service: RoomService) => {
    setSelectedService(service);
    setFormData({
      name: service.name,
      description: service.description,
      price: service.price,
      isActive: service.isActive,
      category: service.category
    });
    setIsEditModalOpen(true);
  };

  const handleAddService = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await roomServiceApi.addRoomService(formData);
      if (response.success) {
        toast.success('Thêm dịch vụ thành công!');
        fetchServices();
        setIsAddModalOpen(false);
      } else {
        toast.error(response.message || 'Thêm dịch vụ thất bại!');
      }
    } catch (error: any) {
      toast.error(error.message || 'Thêm dịch vụ thất bại!');
    }
  };

  const handleUpdateService = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedService) return;
    
    try {
      const response = await roomServiceApi.updateRoomService(selectedService.serviceId, formData);
      if (response.success) {
        toast.success('Cập nhật dịch vụ thành công!');
        fetchServices();
        setIsEditModalOpen(false);
      } else {
        toast.error(response.message || 'Cập nhật dịch vụ thất bại!');
      }
    } catch (error: any) {
      toast.error(error.message || 'Cập nhật dịch vụ thất bại!');
    }
  };

  const handleDeleteService = async (serviceId: number) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa dịch vụ này?')) {
      try {
        const response = await roomServiceApi.deleteRoomService(serviceId);
        if (response.success) {
          toast.success('Xóa dịch vụ thành công!');
          fetchServices();
        } else {
          toast.error(response.message || 'Xóa dịch vụ thất bại!');
        }
      } catch (error: any) {
        toast.error(error.message || 'Xóa dịch vụ thất bại!');
      }
    }
  };

  const filteredServices = services.filter(service => 
    service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div>
      <div className={styles.toolbarSection}>
        <div className={styles.searchBox}>
          <FaSearch />
          <input
            type="text"
            placeholder="Tìm kiếm dịch vụ..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <button className={`${styles.button} ${styles.primaryButton}`} onClick={openAddModal}>
          <FaPlus /> Thêm dịch vụ
        </button>
      </div>

      {loading ? (
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
        </div>
      ) : (
        <div className={styles.tableContainer}>
          <table className={styles.dataTable}>
            <thead>
              <tr>
                <th>ID</th>
                <th>Tên dịch vụ</th>
                <th>Mô tả</th>
                <th>Giá</th>
                <th>Danh mục</th>
                <th>Trạng thái</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {filteredServices.length > 0 ? (
                filteredServices.map((service) => (
                  <tr key={service.serviceId}>
                    <td>{service.serviceId}</td>
                    <td>{service.name}</td>
                    <td>{service.description.length > 30 
                      ? `${service.description.substring(0, 30)}...` 
                      : service.description}
                    </td>
                    <td>{service.price.toLocaleString()} VND</td>
                    <td>{service.category}</td>
                    <td>
                      <span className={`${styles.statusBadge} ${service.isActive ? styles.statusAvailable : styles.statusBooked}`}>
                        {service.isActive ? 'Hoạt động' : 'Không hoạt động'}
                      </span>
                    </td>
                    <td>
                      <button
                        className={`${styles.actionButton} ${styles.editButton}`}
                        onClick={() => openEditModal(service)}
                      >
                        <FaEdit />
                      </button>
                      <button
                        className={`${styles.actionButton} ${styles.deleteButton}`}
                        onClick={() => handleDeleteService(service.serviceId)}
                      >
                        <FaTrash />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} style={{ textAlign: 'center' }}>
                    Không tìm thấy dịch vụ nào
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* Modal thêm dịch vụ */}
      {isAddModalOpen && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h3>Thêm dịch vụ mới</h3>
              <button className={styles.closeButton} onClick={() => setIsAddModalOpen(false)}>×</button>
            </div>
            <form onSubmit={handleAddService}>
              <div className={styles.formGroup}>
                <label>Tên dịch vụ</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>Mô tả</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>
              <div className={styles.formGroup}>
                <label>Giá</label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>Danh mục</label>
                <input
                  type="text"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                />
              </div>
              <div className={styles.formGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                  />
                  Hoạt động
                </label>
              </div>
              <div className={styles.modalFooter}>
                <button type="button" className={styles.cancelButton} onClick={() => setIsAddModalOpen(false)}>
                  Hủy
                </button>
                <button type="submit" className={styles.submitButton}>
                  Thêm dịch vụ
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal chỉnh sửa dịch vụ */}
      {isEditModalOpen && selectedService && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h3>Chỉnh sửa dịch vụ</h3>
              <button className={styles.closeButton} onClick={() => setIsEditModalOpen(false)}>×</button>
            </div>
            <form onSubmit={handleUpdateService}>
              <div className={styles.formGroup}>
                <label>Tên dịch vụ</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>Mô tả</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>
              <div className={styles.formGroup}>
                <label>Giá</label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>Danh mục</label>
                <input
                  type="text"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                />
              </div>
              <div className={styles.formGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                  />
                  Hoạt động
                </label>
              </div>
              <div className={styles.modalFooter}>
                <button type="button" className={styles.cancelButton} onClick={() => setIsEditModalOpen(false)}>
                  Hủy
                </button>
                <button type="submit" className={styles.submitButton}>
                  Cập nhật
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default RoomServiceManagement;
